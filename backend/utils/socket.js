const { Server } = require('socket.io')

const io = new Server({
  maxHttpBufferSize: 1e8,
})

const Socket = {
  emit: function (event, data) {
    console.log(event, data)
    io.sockets.emit(event, data)
  },
  emitToTicket: function (ticketId, event, data) {
    console.log(`Emitting to ticket-${ticketId}:`, event, data)
    io.to(`ticket-${ticketId}`).emit(event, data)
  },
  emitToUser: function (userId, event, data) {
    console.log(`Emitting to user ${userId}:`, event, data)
    io.to(userId).emit(event, data)
  },
  emitToAgents: function (event, data) {
    console.log('Emitting to agents:', event, data)

    // This would require tracking agent socket IDs
    io.sockets.emit(event, data)
  },
}

io.use((socket, next) => {
  const token = socket.handshake.auth.token
  const userId = socket.handshake.auth.userId

  if (token) {
    console.log('yes token')
    socket.auth = true
    socket.userId = userId
    userRooms.set(socket.id, userId)

    socket.join(userId)
    socket.emit('connected', `⚡ ${ socket.id } user just connected to ${ userId }!`)

    next()
  } else {
    console.log('no token')
    socket.auth = false

    socket.emit('connected', '⚡ Visitor Connected')

    next()
  }
})

// Map to store user rooms by socket ID
const userRooms = new Map()
// Map to track online users
const onlineUsers = new Map()

io.on('connection', socket => {
  console.log('Socket authenticated:', socket.auth)
  if (socket.auth) {
    // Import Controllers
    const {
      getTickets,
      getTicket,
      createTicket,
      updateTicket,
      assignTicket,
      deleteTicket,
    } = require('../controllers/ticket.controller')

    const {
      getMessages,
      createMessage,
      updateMessage,
      markAsRead,
      getUnreadCount,
    } = require('../controllers/message.controller')

    // Ticket socket events
    socket.on('ticketController:getTickets', (data) => {
      data.user = socket.userId
      getTickets(data)
    })
    socket.on('ticketController:getTicket', (data) => {
      data.user = socket.userId
      getTicket(data)
    })
    socket.on('ticketController:createTicket', (data) => {
      data.user = socket.userId
      createTicket(data)
    })
    socket.on('ticketController:updateTicket', (data) => {
      data.user = socket.userId
      updateTicket(data)
    })
    socket.on('ticketController:assignTicket', (data) => {
      data.user = socket.userId
      assignTicket(data)
    })
    socket.on('ticketController:deleteTicket', (data) => {
      data.user = socket.userId
      deleteTicket(data)
    })

    // Message socket events
    socket.on('messageController:getMessages', (data) => {
      data.user = socket.userId
      getMessages(data)
    })
    socket.on('messageController:createMessage', (data) => {
      data.user = socket.userId
      createMessage(data)
    })
    socket.on('messageController:updateMessage', (data) => {
      data.user = socket.userId
      updateMessage(data)
    })
    socket.on('messageController:markAsRead', (data) => {
      data.user = socket.userId
      markAsRead(data)
    })
    socket.on('messageController:getUnreadCount', (data) => {
      data.user = socket.userId
      getUnreadCount(data)
    })

    // Join ticket rooms for real-time updates
    socket.on('join-ticket', ticketId => {
      socket.join(`ticket-${ticketId}`)
      console.log(`User ${socket.id} joined ticket room: ticket-${ticketId}`)
    })

    socket.on('leave-ticket', ticketId => {
      socket.leave(`ticket-${ticketId}`)
      console.log(`User ${socket.id} left ticket room: ticket-${ticketId}`)
    })

    // Typing indicators
    socket.on('typing-start', data => {
      socket.to(`ticket-${data.ticketId}`).emit('user-typing', {
        userId: socket.handshake.auth.userId,
        ticketId: data.ticketId,
        isTyping: true,
      })
    })

    socket.on('typing-stop', data => {
      socket.to(`ticket-${data.ticketId}`).emit('user-typing', {
        userId: socket.handshake.auth.userId,
        ticketId: data.ticketId,
        isTyping: false,
      })
    })

  } else {
    const {
      register,
      login,
      forgotPassword,
      resetPassword,
    } = require('../controllers/auth.controller')

    const {
      getUsers,
      getUser,
      createUser,
      updateUser,
      deleteUser,
    } = require('../controllers/user.controller')

    const {
      getCategories,
      getCategory,
      createCategory,
      updateCategory,
      deleteCategory,
    } = require('../controllers/category.controller')

    // Auth controller events
    socket.on('authController:register', register)
    socket.on('authController:login', login)
    socket.on('authController:forgotPassword', forgotPassword)
    socket.on('authController:resetPassword', resetPassword)

    // User controller events
    socket.on('userController:getUsers', getUsers)
    socket.on('userController:getUser', getUser)
    socket.on('userController:createUser', createUser)
    socket.on('userController:updateUser', updateUser)
    socket.on('userController:deleteUser', deleteUser)

    // Category controller events
    socket.on('categoryController:getCategories', getCategories)
    socket.on('categoryController:getCategory', getCategory)
    socket.on('categoryController:createCategory', createCategory)
    socket.on('categoryController:updateCategory', updateCategory)
    socket.on('categoryController:deleteCategory', deleteCategory)
  }

  socket.on('disconnect', () => {
    io.emit('user disconnected')
  })
})

exports.Socket = Socket
exports.io = io
