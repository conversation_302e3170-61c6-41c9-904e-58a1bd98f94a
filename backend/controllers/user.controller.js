const User = require('../models/user')
const ErrorResponse = require('../utils/errorResponse')
const asyncHandler = require('../middleware/async')
const { Socket, io } = require('../utils/socket')

exports.getUsers = asyncHandler(async (req, res, next) => {
  if (res && res.advancedResults) {
    // HTTP request with advancedResults middleware
    res.status(200).json(res.advancedResults)
  } else {
    // Socket request
    const users = await User.find()
    io.to(req.user).emit('userController:getUsers', {
      status: 'success',
      data: users,
    })
  }
})

exports.getUser = asyncHandler(async (req, res, next) => {
  // Get user ID from params or directly from req
  const userId = req.params?.id || req.id

  const user = await User.findById(userId)

  if (!user) {
    if (res) {
      return next(new ErrorResponse(`User not found with id of ${userId}`, 404))
    } else {
      return io.to(req.user).emit('userController:getUser', {
        status: 'error',
        data: {
          message: `User not found with id of ${userId}`,
        },
      })
    }
  }

  if (res) {
    res.status(200).json({
      success: true,
      data: user,
    })
  } else {
    io.to(req.user).emit('userController:getUser', {
      status: 'success',
      data: user,
    })
  }
})

exports.createUser = asyncHandler(async (req, res, next) => {
  // Create user from either req directly or req.body
  const userData = req.body || req

  const user = await User.create(userData)

  if (res) {
    res.status(201).json({
      success: true,
      data: user,
    })
  } else {
    io.to(req.user).emit('userController:createUser', {
      status: 'success',
      data: user,
    })
  }
})

exports.updateUser = asyncHandler(async (req, res, next) => {
  // Get user ID from params or directly from req
  const userId = req.params?.id || req.id

  // Get update data from body or directly from req
  const updateData = req.body || req

  const user = await User.findByIdAndUpdate(userId, updateData, {
    new: true,
    runValidators: true,
  })

  if (res) {
    res.status(200).json({
      success: true,
      data: user,
    })
  } else {
    io.to(req.user).emit('userController:updateUser', {
      status: 'success',
      data: user,
    })
  }
})

exports.deleteUser = asyncHandler(async (req, res, next) => {
  // Get user ID from params or directly from req
  const userId = req.params?.id || req.id

  await User.findByIdAndDelete(userId)

  if (res) {
    res.status(200).json({
      success: true,
      data: {},
    })
  } else {
    io.to(req.user).emit('userController:deleteUser', {
      status: 'success',
      data: {
        message: 'User deleted successfully',
      },
    })
  }
})
