const Category = require('../models/category')
const Ticket = require('../models/ticket')
const ErrorResponse = require('../utils/errorResponse')
const asyncHandler = require('../middleware/async')
const { Socket, io } = require('../utils/socket')

// @desc    Get all categories
// @route   GET /api/v1/categories
// @access  Public
exports.getCategories = asyncHandler(async (req, res, next) => {
  try {
    const categories = await Category.find({ isActive: true })
      .populate('ticketCount')
      .sort('sortOrder name')

    const responseData = {
      count: categories.length,
      categories: categories,
    }

    if (res) {
      res.status(200).json({
        success: true,
        count: categories.length,
        data: categories,
      })
    } else {
      io.to(req.user).emit('categoryController:getCategories', {
        status: 'success',
        data: responseData,
      })
    }
  } catch (error) {
    if (res) {
      return next(error)
    } else {
      io.to(req.user).emit('categoryController:getCategories', {
        status: 'error',
        data: {
          message: error.message || 'Failed to fetch categories',
        },
      })
    }
  }
})

// @desc    Get single category
// @route   GET /api/v1/categories/:id
// @access  Public
exports.getCategory = asyncHandler(async (req, res, next) => {
  try {
    const category = await Category.findById(req.params.id)
      .populate('ticketCount')
      .populate('autoAssignAgent', 'name email')

    if (!category) {
      const error = new ErrorResponse('Category not found', 404)
      if (res) {
        return next(error)
      } else {
        return io.to(req.user).emit('categoryController:getCategory', {
          status: 'error',
          data: {
            message: 'Category not found',
          },
        })
      }
    }

    if (res) {
      res.status(200).json({
        success: true,
        data: category,
      })
    } else {
      io.to(req.user).emit('categoryController:getCategory', {
        status: 'success',
        data: category,
      })
    }
  } catch (error) {
    if (res) {
      return next(error)
    } else {
      io.to(req.user).emit('categoryController:getCategory', {
        status: 'error',
        data: {
          message: error.message || 'Failed to fetch category',
        },
      })
    }
  }
})

// @desc    Create new category
// @route   POST /api/v1/categories
// @access  Private (Admin only)
exports.createCategory = asyncHandler(async (req, res, next) => {
  try {
    // Only admins can create categories
    if (req.user.role !== 'admin') {
      const error = new ErrorResponse('Not authorized to create categories', 403)
      if (res) {
        return next(error)
      } else {
        return io.to(req.user).emit('categoryController:createCategory', {
          status: 'error',
          data: {
            message: 'Not authorized to create categories',
          },
        })
      }
    }

    const categoryData = req.body || req
    const category = await Category.create(categoryData)

    if (res) {
      res.status(201).json({
        success: true,
        data: category,
      })
    } else {
      io.to(req.user).emit('categoryController:createCategory', {
        status: 'success',
        data: category,
      })
    }
  } catch (error) {
    if (res) {
      return next(error)
    } else {
      io.to(req.user).emit('categoryController:createCategory', {
        status: 'error',
        data: {
          message: error.message || 'Failed to create category',
        },
      })
    }
  }
})

// @desc    Update category
// @route   PUT /api/v1/categories/:id
// @access  Private (Admin only)
exports.updateCategory = asyncHandler(async (req, res, next) => {
  try {
    // Only admins can update categories
    if (req.user.role !== 'admin') {
      const error = new ErrorResponse('Not authorized to update categories', 403)
      if (res) {
        return next(error)
      } else {
        return io.to(req.user).emit('categoryController:updateCategory', {
          status: 'error',
          data: {
            message: 'Not authorized to update categories',
          },
        })
      }
    }

    const updateData = req.body || req
    const category = await Category.findByIdAndUpdate(req.params.id, updateData, {
      new: true,
      runValidators: true,
    })

    if (!category) {
      const error = new ErrorResponse('Category not found', 404)
      if (res) {
        return next(error)
      } else {
        return io.to(req.user).emit('categoryController:updateCategory', {
          status: 'error',
          data: {
            message: 'Category not found',
          },
        })
      }
    }

    if (res) {
      res.status(200).json({
        success: true,
        data: category,
      })
    } else {
      io.to(req.user).emit('categoryController:updateCategory', {
        status: 'success',
        data: category,
      })
    }
  } catch (error) {
    if (res) {
      return next(error)
    } else {
      io.to(req.user).emit('categoryController:updateCategory', {
        status: 'error',
        data: {
          message: error.message || 'Failed to update category',
        },
      })
    }
  }
})

// @desc    Delete category
// @route   DELETE /api/v1/categories/:id
// @access  Private (Admin only)
exports.deleteCategory = asyncHandler(async (req, res, next) => {
  try {
    // Only admins can delete categories
    if (req.user.role !== 'admin') {
      const error = new ErrorResponse('Not authorized to delete categories', 403)
      if (res) {
        return next(error)
      } else {
        return io.to(req.user).emit('categoryController:deleteCategory', {
          status: 'error',
          data: {
            message: 'Not authorized to delete categories',
          },
        })
      }
    }

    const category = await Category.findById(req.params.id)

    if (!category) {
      const error = new ErrorResponse('Category not found', 404)
      if (res) {
        return next(error)
      } else {
        return io.to(req.user).emit('categoryController:deleteCategory', {
          status: 'error',
          data: {
            message: 'Category not found',
          },
        })
      }
    }

    // Check if category has tickets
    const ticketCount = await Ticket.countDocuments({ category: req.params.id })

    if (ticketCount > 0) {
      const error = new ErrorResponse('Cannot delete category with existing tickets', 400)
      if (res) {
        return next(error)
      } else {
        return io.to(req.user).emit('categoryController:deleteCategory', {
          status: 'error',
          data: {
            message: 'Cannot delete category with existing tickets',
          },
        })
      }
    }

    await category.deleteOne()

    if (res) {
      res.status(200).json({
        success: true,
        data: {},
      })
    } else {
      io.to(req.user).emit('categoryController:deleteCategory', {
        status: 'success',
        data: {
          categoryId: req.params.id,
          message: 'Category deleted successfully',
        },
      })
    }
  } catch (error) {
    if (res) {
      return next(error)
    } else {
      io.to(req.user).emit('categoryController:deleteCategory', {
        status: 'error',
        data: {
          message: error.message || 'Failed to delete category',
        },
      })
    }
  }
})

// @desc    Get category statistics
// @route   GET /api/v1/categories/:id/stats
// @access  Private (Agent/Admin)
exports.getCategoryStats = asyncHandler(async (req, res, next) => {
  // Only agents and admins can view stats
  if (!['agent', 'admin'].includes(req.user.role)) {
    return next(new ErrorResponse('Not authorized to view category statistics', 403))
  }

  const category = await Category.findById(req.params.id)

  if (!category) {
    return next(new ErrorResponse('Category not found', 404))
  }

  // Get ticket statistics for this category
  const stats = await Ticket.aggregate([
    { $match: { category: category._id } },
    {
      $group: {
        _id: '$status',
        count: { $sum: 1 },
        avgResolutionTime: { $avg: '$actualResolutionTime' },
      },
    },
  ])

  // Get total tickets
  const totalTickets = await Ticket.countDocuments({ category: req.params.id })

  // Get tickets by priority
  const priorityStats = await Ticket.aggregate([
    { $match: { category: category._id } },
    {
      $group: {
        _id: '$priority',
        count: { $sum: 1 },
      },
    },
  ])

  // Get recent activity (last 30 days)
  const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)

  const recentTickets = await Ticket.countDocuments({
    category: req.params.id,
    createdAt: { $gte: thirtyDaysAgo },
  })

  res.status(200).json({
    success: true,
    data: {
      category,
      totalTickets,
      statusStats: stats,
      priorityStats,
      recentTickets,
    },
  })
})
