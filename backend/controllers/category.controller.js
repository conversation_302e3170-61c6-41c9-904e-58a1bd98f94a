const Category = require('../models/category')
const Ticket = require('../models/ticket')
const ErrorResponse = require('../utils/errorResponse')
const asyncHandler = require('../middleware/async')
const { Socket, io } = require('../utils/socket')

// @desc    Get all categories
// @route   Socket categoryController:getCategories
// @access  Public
exports.getCategories = asyncHandler(async (req, res, next) => {
  try {
    const categories = await Category.find({ isActive: true })
      .populate('ticketCount')
      .sort('sortOrder name')

    const responseData = {
      count: categories.length,
      categories: categories,
    }

    io.to(req.user).emit('categoryController:getCategories', {
      status: 'success',
      data: responseData,
    })
  } catch (error) {
    io.to(req.user).emit('categoryController:getCategories', {
      status: 'error',
      data: {
        message: error.message || 'Failed to fetch categories',
      },
    })
  }
})

// @desc    Get single category
// @route   Socket categoryController:getCategory
// @access  Public
exports.getCategory = asyncHandler(async (req, res, next) => {
  try {
    const categoryId = req.params?.id || req.id
    const category = await Category.findById(categoryId)
      .populate('ticketCount')
      .populate('autoAssignAgent', 'name email')

    if (!category) {
      return io.to(req.user).emit('categoryController:getCategory', {
        status: 'error',
        data: {
          message: 'Category not found',
        },
      })
    }

    io.to(req.user).emit('categoryController:getCategory', {
      status: 'success',
      data: category,
    })
  } catch (error) {
    io.to(req.user).emit('categoryController:getCategory', {
      status: 'error',
      data: {
        message: error.message || 'Failed to fetch category',
      },
    })
  }
})

// @desc    Create new category
// @route   Socket categoryController:createCategory
// @access  Private (Admin only)
exports.createCategory = asyncHandler(async (req, res, next) => {
  try {
    // Only admins can create categories
    if (req.user.role !== 'admin') {
      return io.to(req.user).emit('categoryController:createCategory', {
        status: 'error',
        data: {
          message: 'Not authorized to create categories',
        },
      })
    }

    const categoryData = req.body || req
    const category = await Category.create(categoryData)

    io.to(req.user).emit('categoryController:createCategory', {
      status: 'success',
      data: category,
    })
  } catch (error) {
    io.to(req.user).emit('categoryController:createCategory', {
      status: 'error',
      data: {
        message: error.message || 'Failed to create category',
      },
    })
  }
})

// @desc    Update category
// @route   Socket categoryController:updateCategory
// @access  Private (Admin only)
exports.updateCategory = asyncHandler(async (req, res, next) => {
  try {
    // Only admins can update categories
    if (req.user.role !== 'admin') {
      return io.to(req.user).emit('categoryController:updateCategory', {
        status: 'error',
        data: {
          message: 'Not authorized to update categories',
        },
      })
    }

    const categoryId = req.params?.id || req.id
    const updateData = req.body || req
    const category = await Category.findByIdAndUpdate(categoryId, updateData, {
      new: true,
      runValidators: true,
    })

    if (!category) {
      return io.to(req.user).emit('categoryController:updateCategory', {
        status: 'error',
        data: {
          message: 'Category not found',
        },
      })
    }

    io.to(req.user).emit('categoryController:updateCategory', {
      status: 'success',
      data: category,
    })
  } catch (error) {
    io.to(req.user).emit('categoryController:updateCategory', {
      status: 'error',
      data: {
        message: error.message || 'Failed to update category',
      },
    })
  }
})

// @desc    Delete category
// @route   Socket categoryController:deleteCategory
// @access  Private (Admin only)
exports.deleteCategory = asyncHandler(async (req, res, next) => {
  try {
    // Only admins can delete categories
    if (req.user.role !== 'admin') {
      return io.to(req.user).emit('categoryController:deleteCategory', {
        status: 'error',
        data: {
          message: 'Not authorized to delete categories',
        },
      })
    }

    const categoryId = req.params?.id || req.id
    const category = await Category.findById(categoryId)

    if (!category) {
      return io.to(req.user).emit('categoryController:deleteCategory', {
        status: 'error',
        data: {
          message: 'Category not found',
        },
      })
    }

    // Check if category has tickets
    const ticketCount = await Ticket.countDocuments({ category: categoryId })

    if (ticketCount > 0) {
      return io.to(req.user).emit('categoryController:deleteCategory', {
        status: 'error',
        data: {
          message: 'Cannot delete category with existing tickets',
        },
      })
    }

    await category.deleteOne()

    io.to(req.user).emit('categoryController:deleteCategory', {
      status: 'success',
      data: {
        categoryId: categoryId,
        message: 'Category deleted successfully',
      },
    })
  } catch (error) {
    io.to(req.user).emit('categoryController:deleteCategory', {
      status: 'error',
      data: {
        message: error.message || 'Failed to delete category',
      },
    })
  }
})

// @desc    Get category statistics
// @route   GET /api/v1/categories/:id/stats
// @access  Private (Agent/Admin)
exports.getCategoryStats = asyncHandler(async (req, res, next) => {
  // Only agents and admins can view stats
  if (!['agent', 'admin'].includes(req.user.role)) {
    return next(new ErrorResponse('Not authorized to view category statistics', 403))
  }

  const category = await Category.findById(req.params.id)

  if (!category) {
    return next(new ErrorResponse('Category not found', 404))
  }

  // Get ticket statistics for this category
  const stats = await Ticket.aggregate([
    { $match: { category: category._id } },
    {
      $group: {
        _id: '$status',
        count: { $sum: 1 },
        avgResolutionTime: { $avg: '$actualResolutionTime' },
      },
    },
  ])

  // Get total tickets
  const totalTickets = await Ticket.countDocuments({ category: req.params.id })

  // Get tickets by priority
  const priorityStats = await Ticket.aggregate([
    { $match: { category: category._id } },
    {
      $group: {
        _id: '$priority',
        count: { $sum: 1 },
      },
    },
  ])

  // Get recent activity (last 30 days)
  const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)

  const recentTickets = await Ticket.countDocuments({
    category: req.params.id,
    createdAt: { $gte: thirtyDaysAgo },
  })

  res.status(200).json({
    success: true,
    data: {
      category,
      totalTickets,
      statusStats: stats,
      priorityStats,
      recentTickets,
    },
  })
})
