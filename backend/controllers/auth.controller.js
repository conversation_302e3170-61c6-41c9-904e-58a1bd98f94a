const User = require('../models/user')
const ErrorResponse = require('../utils/errorResponse')
const asyncHandler = require('../middleware/async')
const crypto = require('crypto')
const sendEmail = require('../utils/sendEmail')
const { Socket, io } = require('../utils/socket')

exports.register = asyncHandler(async (req, res, next) => {
  const { name, email, password, role, supportAuthId } = req

  const user = await User.create({
    name,
    email,
    password,
    role,
    supportAuthId,
  })

  const token = user.getSignedJwtToken()

  if (res) {
    sendTokenResponse(user, 200, res)
  } else {
    io.to(req.user).emit('authController:register', {
      status: 'success',
      data: {
        token,
        user,
      },
    })
  }
})

exports.login = asyncHandler(async (req, res, next) => {
  const { email, password } = req

  // Validate email and password
  if (!email || !password) {
    if (res) {
      return next(new ErrorResponse('Please provide an email and password', 400))
    } else {
      return io.to(req.user).emit('authController:login', {
        status: 'error',
        data: {
          message: 'Please Enter An Email / Password!',
        },
      })
    }
  }

  // Check for user
  const user = await User.findOne({ email }).select('+password')

  if (!user) {
    if (res) {
      return next(new ErrorResponse('Invalid credentials', 401))
    } else {
      return io.to(req.user).emit('authController:login', {
        status: 'error',
        data: {
          message: 'Invalid User Credentials!',
        },
      })
    }
  }

  // Check if password matches
  const isMatched = await user.matchPassword(password)

  if (!isMatched) {
    if (res) {
      return next(new ErrorResponse('Invalid credentials', 401))
    } else {
      return io.to(req.user).emit('authController:login', {
        status: 'error',
        data: {
          message: 'Invalid User Credentials!',
        },
      })
    }
  }

  // If we get here, authentication is successful
  const token = user.getSignedJwtToken()

  if (res) {
    sendTokenResponse(user, 200, res)
  } else {
    io.to(req.user).emit('authController:login', {
      status: 'success',
      data: {
        message: 'Logged In Successfully!',
        token: token,
        user: user,
      },
    })
  }
})

exports.logout = asyncHandler(async (req, res, next) => {
  if (res) {
    res.cookie('token', 'none', {
      expires: new Date(Date.now() + 10 * 1000),
      httpOnly: true,
    })

    res.status(200).json({
      success: true,
      data: {},
    })
  } else {
    io.to(req.user).emit('authController:logout', {
      status: 'success',
      data: {
        message: 'Logged out successfully',
      },
    })
  }
})

exports.getMe = asyncHandler(async (req, res, next) => {
  const user = await User.findById(req.user.id)

  if (res) {
    res.status(200).json({
      success: true,
      data: user,
    })
  } else {
    io.to(req.user).emit('authController:getMe', {
      status: 'success',
      data: user,
    })
  }
})

exports.updateDetails = asyncHandler(async (req, res, next) => {
  const fieldsToUpdate = {
    name: req.name || req.body?.name,
    email: req.email || req.body?.email,
  }

  const user = await User.findByIdAndUpdate(req.user.id, fieldsToUpdate, {
    new: true,
    runValidators: true,
  })

  if (res) {
    res.status(200).json({
      success: true,
      data: user,
    })
  } else {
    io.to(req.user).emit('authController:updateDetails', {
      status: 'success',
      data: user,
    })
  }
})

exports.updatePassword = asyncHandler(async (req, res, next) => {
  const user = await User.findById(req.user.id).select('+password')
  const currentPassword = req.currentPassword || req.body?.currentPassword
  const newPassword = req.newPassword || req.body?.newPassword

  if (!(await user.matchPassword(currentPassword))) {
    if (res) {
      return next(new ErrorResponse('Password is incorrect!', 401))
    } else {
      return io.to(req.user).emit('authController:updatePassword', {
        status: 'error',
        data: {
          message: 'Password is incorrect!',
        },
      })
    }
  }

  user.password = newPassword
  await user.save()

  if (res) {
    sendTokenResponse(user, 200, res)
  } else {
    const token = user.getSignedJwtToken()
    io.to(req.user).emit('authController:updatePassword', {
      status: 'success',
      data: {
        token,
        user,
      },
    })
  }
})

exports.forgotPassword = asyncHandler(async (req, res, next) => {
  const email = req.email || req.body?.email

  const user = await User.findOne({ email })

  if (!user) {
    if (res) {
      return next(new ErrorResponse('No user exists with that email', 404))
    } else {
      return io.to(req.user).emit('authController:forgotPassword', {
        status: 'error',
        data: {
          message: 'No user exists with that email',
        },
      })
    }
  }

  const resetToken = user.getResetPasswordToken()

  await user.save({ validateBeforeSave: false })

  // Get the host from either HTTP request or a default value for socket
  const host = (req.get && req.get('host')) || process.env.APP_HOST || 'localhost:3000'
  const protocol = req.protocol || 'http'

  const resetUrl = `${protocol}://${host}/api/v1/auth/resetpassword/${resetToken}`

  const message = `You are receiving this email because have requested a password reset. Please click the link to reset password ${resetUrl}`

  try {
    await sendEmail({
      email: user.email,
      subject: 'Password Reset',
      message,
    })

    if (res) {
      res.status(200).json({
        success: true,
        data: 'Email Sent',
      })
    } else {
      io.to(req.user).emit('authController:forgotPassword', {
        status: 'success',
        data: {
          message: 'Email Sent',
        },
      })
    }
  } catch (err) {
    console.log(err)
    user.resetPasswordToken = undefined
    user.resetPasswordExpire = undefined

    await user.save({ validateBeforeSave: false })

    if (res) {
      return next(new ErrorResponse('Email could not be sent', 500))
    } else {
      return io.to(req.user).emit('authController:forgotPassword', {
        status: 'error',
        data: {
          message: 'Email could not be sent',
        },
      })
    }
  }
})

exports.resetPassword = asyncHandler(async (req, res, next) => {
  // Get resettoken from params or body
  const resettoken = req.params?.resettoken || req.resettoken

  const resetPasswordToken = crypto.createHash('sha256').update(resettoken).digest('hex')

  const user = await User.findOne({
    resetPasswordToken,
    resetPasswordExpire: { $gt: Date.now() },
  })

  if (!user) {
    if (res) {
      return next(new ErrorResponse('Invalid Token', 400))
    } else {
      return io.to(req.user).emit('authController:resetPassword', {
        status: 'error',
        data: {
          message: 'Invalid Token',
        },
      })
    }
  }

  // Get password from req or req.body
  const password = req.password || req.body?.password

  user.password = password
  user.resetPasswordToken = undefined
  user.resetPasswordExpire = undefined

  await user.save()

  if (res) {
    sendTokenResponse(user, 200, res)
  } else {
    const token = user.getSignedJwtToken()
    io.to(req.user).emit('authController:resetPassword', {
      status: 'success',
      data: {
        token,
        user,
      },
    })
  }
})

const sendTokenResponse = (user, statusCode, res) => {
  const token = user.getSignedJwtToken()

  const options = {
    expires: new Date(Date.now() + process.env.JWT_COOKIE_EXPIRE * 24 * 60 * 60 * 1000),
    httpOnly: true,
  }

  if (process.env.NODE_ENV === 'production') {
    options.secure = true
  }

  res.status(statusCode)
    .cookie('token', token, options)
    .json({
      success: true,
      token,
      user,
    })

  return token
}
