import { createMongoAbility } from '@casl/ability'
import { abilitiesPlugin } from '@casl/vue'

export default function (app) {
  const userAbilityRules = JSON.parse(sessionStorage.getItem('userAbilities'))
  const initialAbility = createMongoAbility(userAbilityRules ?? [])

  console.log('initialAbility', userAbilityRules)

  app.use(abilitiesPlugin, initialAbility, {
    useGlobalProperties: true,
  })
}
