import { defineStore } from 'pinia'
import { socket } from '@socket/socket'
import { ref, computed } from 'vue'
import { toast } from 'vue3-toastify'

export const useCategoryStore = defineStore('categories', () => {
  const categories = ref([])
  const currentCategory = ref(null)
  const loading = ref(false)
  const error = ref(null)

  // Computed
  const activeCategories = computed(() => 
    categories.value.filter(category => category.isActive),
  )

  const sortedCategories = computed(() => 
    [...categories.value].sort((a, b) => a.sortOrder - b.sortOrder),
  )

  // Actions
  function $reset() {
    categories.value = []
    currentCategory.value = null
    loading.value = false
    error.value = null
  }

  function setLoading(value) {
    loading.value = value
  }

  function setError(errorMessage) {
    error.value = errorMessage
    if (errorMessage) {
      toast.error(errorMessage)
    }
  }

  // Socket Events
  function getCategories() {
    console.log('🔍 Frontend: Requesting categories via socket...')
    setLoading(true)
    socket.emit('categoryController:getCategories', {})
  }

  function getCategory(categoryId) {
    setLoading(true)
    socket.emit('categoryController:getCategory', { params: { id: categoryId } })
  }

  function createCategory(categoryData) {
    setLoading(true)
    socket.emit('categoryController:createCategory', { body: categoryData })
  }

  function updateCategory(categoryId, updateData) {
    setLoading(true)
    socket.emit('categoryController:updateCategory', {
      params: { id: categoryId },
      body: updateData
    })
  }

  function deleteCategory(categoryId) {
    setLoading(true)
    socket.emit('categoryController:deleteCategory', { params: { id: categoryId } })
  }

  async function getCategoryStats(categoryId) {
    try {
      setLoading(true)

      const token = sessionStorage.getItem('accessToken')
      
      const response = await ofetch(`/api/v1/categories/${categoryId}/stats`, {
        baseURL: import.meta.env.VITE_API_BASE_URL,
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      })
      
      if (response.success) {
        return response.data
      } else {
        setError(response.message || 'Failed to fetch category statistics')
        
        return null
      }
    } catch (err) {
      setError(err.message || 'Failed to fetch category statistics')
      
      return null
    } finally {
      setLoading(false)
    }
  }

  function getCategoryById(categoryId) {
    return categories.value.find(c => c._id === categoryId)
  }

  function getCategoryBySlug(slug) {
    return categories.value.find(c => c.slug === slug)
  }

  // Socket Listeners
  socket.on('categoryController:getCategories', response => {
    console.log('📥 Frontend: Received categories response:', response)
    setLoading(false)
    if (response.status === 'success') {
      const categoriesData = response.data.categories || response.data
      console.log('✅ Frontend: Setting categories:', categoriesData)
      categories.value = categoriesData
    } else {
      console.error('❌ Frontend: Categories error:', response.data?.message)
      setError(response.data?.message || 'Failed to fetch categories')
    }
  })

  socket.on('categoryController:getCategory', response => {
    setLoading(false)
    if (response.status === 'success') {
      currentCategory.value = response.data
    } else {
      setError(response.data?.message || 'Failed to fetch category')
    }
  })

  socket.on('categoryController:createCategory', response => {
    setLoading(false)
    if (response.status === 'success') {
      categories.value.push(response.data)
      toast.success('📁 Category created successfully!')
    } else {
      setError(response.data?.message || 'Failed to create category')
      toast.error(`❌ Failed to create category: ${response.data?.message || 'Unknown error'}`)
    }
  })

  socket.on('categoryController:updateCategory', response => {
    setLoading(false)
    if (response.status === 'success') {
      const index = categories.value.findIndex(c => c._id === response.data._id)
      if (index !== -1) {
        categories.value[index] = response.data
      }
      if (currentCategory.value?._id === response.data._id) {
        currentCategory.value = response.data
      }
      toast.success('✏️ Category updated successfully!')
    } else {
      setError(response.data?.message || 'Failed to update category')
      toast.error(`❌ Failed to update category: ${response.data?.message || 'Unknown error'}`)
    }
  })

  socket.on('categoryController:deleteCategory', response => {
    setLoading(false)
    if (response.status === 'success') {
      const deletedCategoryId = response.data?.categoryId || response.data?._id
      if (deletedCategoryId) {
        categories.value = categories.value.filter(c => c._id !== deletedCategoryId)
      }
      toast.success('🗑️ Category deleted successfully!')
    } else {
      setError(response.data?.message || 'Failed to delete category')
      toast.error(`❌ Failed to delete category: ${response.data?.message || 'Unknown error'}`)
    }
  })

  return {
    // State
    categories,
    currentCategory,
    loading,
    error,
    
    // Computed
    activeCategories,
    sortedCategories,
    
    // Actions
    $reset,
    setLoading,
    setError,
    getCategories,
    getCategory,
    createCategory,
    updateCategory,
    deleteCategory,
    getCategoryStats,
    getCategoryById,
    getCategoryBySlug,
  }
}, {
  persistedState: {
    persist: true,
    paths: ['categories'],
  },
})
