import { defineStore } from 'pinia'
import { socket } from '@socket/socket'
import { ref, computed } from 'vue'
import { toast } from 'vue3-toastify'

export const useMessageStore = defineStore('messages', () => {
  const messages = ref([])
  const unreadCount = ref(0)
  const loading = ref(false)
  const error = ref(null)
  const typingUsers = ref([])
  const currentTicketId = ref(null)

  // Computed
  const sortedMessages = computed(() => 
    [...messages.value].sort((a, b) => new Date(a.createdAt) - new Date(b.createdAt)),
  )

  const publicMessages = computed(() => 
    messages.value.filter(msg => !msg.isInternal),
  )

  const internalMessages = computed(() => 
    messages.value.filter(msg => msg.isInternal),
  )

  // Actions
  function $reset() {
    messages.value = []
    unreadCount.value = 0
    loading.value = false
    error.value = null
    typingUsers.value = []
    currentTicketId.value = null
  }

  function setLoading(value) {
    loading.value = value
  }

  function setError(errorMessage) {
    error.value = errorMessage
    if (errorMessage) {
      toast.error(errorMessage)
    }
  }

  function setCurrentTicket(ticketId) {
    currentTicketId.value = ticketId
    messages.value = [] // Clear previous messages
  }

  function joinTicketRoom(ticketId) {
    socket.emit('join-ticket', ticketId)
    setCurrentTicket(ticketId)
  }

  function leaveTicketRoom(ticketId) {
    socket.emit('leave-ticket', ticketId)
    currentTicketId.value = null
    messages.value = []
    typingUsers.value = []
  }

  function startTyping(ticketId) {
    socket.emit('typing-start', { ticketId })
  }

  function stopTyping(ticketId) {
    socket.emit('typing-stop', { ticketId })
  }

  // Socket Events
  function getMessages(ticketId) {
    setLoading(true)
    socket.emit('messageController:getMessages', { params: { ticketId } })
  }

  function createMessage(ticketId, messageData, files = []) {
    if (files.length > 0) {
      // Handle file upload with message
      createMessageWithFiles(ticketId, messageData, files)
    } else {
      // Regular message without files
      socket.emit('messageController:createMessage', {
        params: { ticketId },
        body: messageData,
      })
    }
  }

  async function createMessageWithFiles(ticketId, messageData, files) {
    try {
      setLoading(true)

      // First create the message
      socket.emit('messageController:createMessage', {
        params: { ticketId },
        body: messageData,
      })

      // Then upload files if any
      if (files.length > 0) {
        await uploadFiles(ticketId, null, files)
      }
    } catch (error) {
      setError('Failed to send message with attachments')
    } finally {
      setLoading(false)
    }
  }

  async function uploadFiles(ticketId, messageId, files) {
    const token = sessionStorage.getItem('accessToken')

    for (const file of files) {
      try {
        const formData = new FormData()

        formData.append('file', file)
        if (ticketId) formData.append('ticketId', ticketId)
        if (messageId) formData.append('messageId', messageId)

        const response = await fetch(`${import.meta.env.VITE_API_BASE_URL}/api/v1/attachments/upload`, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${token}`,
          },
          body: formData,
        })

        const result = await response.json()

        if (!result.success) {
          throw new Error(result.message || 'Upload failed')
        }
      } catch (error) {
        console.error('File upload error:', error)
        setError(`Failed to upload ${file.name}: ${error.message}`)
      }
    }
  }

  function updateMessage(messageId, updateData) {
    socket.emit('messageController:updateMessage', { 
      params: { id: messageId }, 
      body: updateData, 
    })
  }

  function deleteMessage(messageId) {
    socket.emit('messageController:deleteMessage', { params: { id: messageId } })
  }

  function markAsRead(messageId) {
    socket.emit('messageController:markAsRead', { params: { id: messageId } })
  }

  function getUnreadCount() {
    socket.emit('messageController:getUnreadCount', {})
  }

  async function downloadAttachment(attachmentId, filename) {
    try {
      const token = sessionStorage.getItem('accessToken')

      const response = await fetch(`${import.meta.env.VITE_API_BASE_URL}/api/v1/attachments/${attachmentId}/download`, {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      })

      if (!response.ok) {
        throw new Error('Download failed')
      }

      // Create blob and download
      const blob = await response.blob()
      const url = window.URL.createObjectURL(blob)
      const a = document.createElement('a')

      a.href = url
      a.download = filename
      document.body.appendChild(a)
      a.click()
      window.URL.revokeObjectURL(url)
      document.body.removeChild(a)
    } catch (error) {
      setError(`Failed to download ${filename}`)
    }
  }

  async function deleteAttachment(attachmentId) {
    try {
      const token = sessionStorage.getItem('accessToken')

      const response = await fetch(`${import.meta.env.VITE_API_BASE_URL}/api/v1/attachments/${attachmentId}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      })

      const result = await response.json()

      if (!result.success) {
        throw new Error(result.message || 'Delete failed')
      }

      return true
    } catch (error) {
      setError('Failed to delete attachment')
      
      return false
    }
  }

  // Socket Listeners
  socket.on('messageController:getMessages', response => {
    setLoading(false)
    if (response.status === 'success') {
      messages.value = response.data
    } else {
      setError(response.data?.message || 'Failed to fetch messages')
    }
  })

  socket.on('messageController:createMessage', response => {
    if (response.status === 'success') {
      messages.value.push(response.data)
      toast.success('💬 Message sent successfully!')
    } else {
      setError(response.data?.message || 'Failed to send message')
      toast.error(`❌ Failed to send message: ${response.data?.message || 'Unknown error'}`)
    }
  })

  socket.on('messageController:updateMessage', response => {
    if (response.status === 'success') {
      const index = messages.value.findIndex(m => m._id === response.data._id)
      if (index !== -1) {
        messages.value[index] = response.data
      }
      toast.success('✏️ Message updated successfully!')
    } else {
      setError(response.data?.message || 'Failed to update message')
      toast.error(`❌ Failed to update message: ${response.data?.message || 'Unknown error'}`)
    }
  })

  socket.on('messageController:deleteMessage', response => {
    if (response.status === 'success') {
      const deletedMessageId = response.data?.messageId || response.data?._id
      if (deletedMessageId) {
        messages.value = messages.value.filter(m => m._id !== deletedMessageId)
      }
      toast.success('🗑️ Message deleted successfully!')
    } else {
      setError(response.data?.message || 'Failed to delete message')
      toast.error(`❌ Failed to delete message: ${response.data?.message || 'Unknown error'}`)
    }
  })

  socket.on('messageController:markAsRead', response => {
    if (response.status === 'success') {
      const index = messages.value.findIndex(m => m._id === response.data._id)
      if (index !== -1) {
        messages.value[index] = response.data
      }
    } else if (response.status === 'error') {
      setError(response.data?.message || 'Failed to mark message as read')
    }
  })

  socket.on('messageController:getUnreadCount', response => {
    if (response.status === 'success') {
      unreadCount.value = response.data.unreadCount || response.data
    } else if (response.status === 'error') {
      setError(response.data?.message || 'Failed to get unread count')
    }
  })

  // Real-time updates
  socket.on('message:created', data => {
    if (data.ticketId === currentTicketId.value) {
      messages.value.push(data.message)

      // Auto-mark as read if user is viewing the ticket
      const user = JSON.parse(sessionStorage.getItem('userData'))
      if (user && data.message.sender._id !== user._id) {
        setTimeout(() => markAsRead(data.message._id), 1000)
      }
    } else {
      // Show notification for messages in other tickets
      toast.info(`New message in ticket: ${data.message.ticket?.title || 'Unknown'}`)
      unreadCount.value++
    }
  })

  socket.on('attachment:uploaded', data => {
    if (data.attachment.ticket === currentTicketId.value) {
      // Find the message and add the attachment
      const messageIndex = messages.value.findIndex(m => m._id === data.attachment.message)
      if (messageIndex !== -1) {
        if (!messages.value[messageIndex].attachments) {
          messages.value[messageIndex].attachments = []
        }
        messages.value[messageIndex].attachments.push(data.attachment)
      }

      toast.success(`File uploaded: ${data.attachment.originalName}`)
    }
  })

  socket.on('attachment:deleted', data => {
    if (data.ticketId === currentTicketId.value) {
      // Remove attachment from all messages
      messages.value.forEach(message => {
        if (message.attachments) {
          message.attachments = message.attachments.filter(att => att._id !== data.attachmentId)
        }
      })
    }
  })

  socket.on('message:updated', data => {
    if (data.message.ticket === currentTicketId.value) {
      const index = messages.value.findIndex(m => m._id === data.message._id)
      if (index !== -1) {
        messages.value[index] = data.message
      }
    }
  })

  socket.on('message:deleted', data => {
    if (data.ticketId === currentTicketId.value) {
      messages.value = messages.value.filter(m => m._id !== data.messageId)
    }
  })

  socket.on('user-typing', data => {
    if (data.ticketId === currentTicketId.value) {
      const user = JSON.parse(sessionStorage.getItem('userData'))
      if (user && data.userId !== user._id) {
        if (data.isTyping) {
          if (!typingUsers.value.includes(data.userId)) {
            typingUsers.value.push(data.userId)
          }
        } else {
          typingUsers.value = typingUsers.value.filter(id => id !== data.userId)
        }
      }
    }
  })

  return {
    // State
    messages,
    unreadCount,
    loading,
    error,
    typingUsers,
    currentTicketId,
    
    // Computed
    sortedMessages,
    publicMessages,
    internalMessages,
    
    // Actions
    $reset,
    setLoading,
    setError,
    setCurrentTicket,
    joinTicketRoom,
    leaveTicketRoom,
    startTyping,
    stopTyping,
    getMessages,
    createMessage,
    createMessageWithFiles,
    uploadFiles,
    updateMessage,
    deleteMessage,
    markAsRead,
    getUnreadCount,
    downloadAttachment,
    deleteAttachment,
  }
}, {
  persistedState: {
    persist: true,
    paths: ['unreadCount'],
  },
})
