import { defineStore } from 'pinia'
import { socket } from '@socketio/socket'
import { ref, computed } from 'vue'
import { toast } from 'vue3-toastify'

export const useTicketStore = defineStore('tickets', () => {
  const tickets = ref([])
  const currentTicket = ref(null)
  const loading = ref(false)
  const error = ref(null)

  // Computed
  const openTickets = computed(() => 
    tickets.value.filter(ticket => ticket.status === 'open'),
  )
  
  const inProgressTickets = computed(() => 
    tickets.value.filter(ticket => ticket.status === 'in-progress'),
  )
  
  const resolvedTickets = computed(() => 
    tickets.value.filter(ticket => ticket.status === 'resolved'),
  )
  
  const closedTickets = computed(() => 
    tickets.value.filter(ticket => ticket.status === 'closed'),
  )

  const myTickets = computed(() => {
    const user = JSON.parse(sessionStorage.getItem('userData'))
    if (!user) return []
    
    return tickets.value.filter(ticket => ticket.user._id === user._id)
  })

  const assignedTickets = computed(() => {
    const user = JSON.parse(sessionStorage.getItem('userData'))
    if (!user) return []
    
    return tickets.value.filter(ticket => ticket.assignedAgent?._id === user._id)
  })

  // Actions
  function $reset() {
    tickets.value = []
    currentTicket.value = null
    loading.value = false
    error.value = null
  }

  function setLoading(value) {
    loading.value = value
  }

  function setError(errorMessage) {
    error.value = errorMessage
    if (errorMessage) {
      toast.error(errorMessage)
    }
  }

  // Socket Events
  function getTickets() {
    setLoading(true)
    socket.emit('ticketController:getTickets', {})
  }

  function getTicket(ticketId) {
    setLoading(true)
    socket.emit('ticketController:getTicket', { params: { id: ticketId } })
  }

  function createTicket(ticketData) {
    setLoading(true)
    socket.emit('ticketController:createTicket', { body: ticketData })
  }

  function updateTicket(ticketId, updateData) {
    setLoading(true)
    socket.emit('ticketController:updateTicket', { 
      params: { id: ticketId }, 
      body: updateData, 
    })
  }

  function assignTicket(ticketId, agentId) {
    setLoading(true)
    socket.emit('ticketController:assignTicket', { 
      params: { id: ticketId }, 
      body: { agentId }, 
    })
  }

  function deleteTicket(ticketId) {
    setLoading(true)
    socket.emit('ticketController:deleteTicket', { params: { id: ticketId } })
  }

  // Socket Listeners
  socket.on('ticketController:getTickets', response => {
    setLoading(false)
    if (response.success) {
      tickets.value = response.data
    } else {
      setError(response.message || 'Failed to fetch tickets')
    }
  })

  socket.on('ticketController:getTicket', response => {
    setLoading(false)
    if (response.success) {
      currentTicket.value = response.data
    } else {
      setError(response.message || 'Failed to fetch ticket')
    }
  })

  socket.on('ticketController:createTicket', response => {
    setLoading(false)
    if (response.success) {
      tickets.value.unshift(response.data)
      toast.success('Ticket created successfully!')
    } else {
      setError(response.message || 'Failed to create ticket')
    }
  })

  socket.on('ticketController:updateTicket', response => {
    setLoading(false)
    if (response.success) {
      const index = tickets.value.findIndex(t => t._id === response.data._id)
      if (index !== -1) {
        tickets.value[index] = response.data
      }
      if (currentTicket.value?._id === response.data._id) {
        currentTicket.value = response.data
      }
      toast.success('Ticket updated successfully!')
    } else {
      setError(response.message || 'Failed to update ticket')
    }
  })

  socket.on('ticketController:assignTicket', response => {
    setLoading(false)
    if (response.success) {
      const index = tickets.value.findIndex(t => t._id === response.data._id)
      if (index !== -1) {
        tickets.value[index] = response.data
      }
      if (currentTicket.value?._id === response.data._id) {
        currentTicket.value = response.data
      }
      toast.success('Ticket assigned successfully!')
    } else {
      setError(response.message || 'Failed to assign ticket')
    }
  })

  socket.on('ticketController:deleteTicket', response => {
    setLoading(false)
    if (response.success) {
      tickets.value = tickets.value.filter(t => t._id !== response.ticketId)
      toast.success('Ticket deleted successfully!')
    } else {
      setError(response.message || 'Failed to delete ticket')
    }
  })

  // Real-time updates
  socket.on('ticket:created', data => {
    tickets.value.unshift(data.ticket)
    toast.info(`New ticket created: ${data.ticket.title}`)
  })

  socket.on('ticket:updated', data => {
    const index = tickets.value.findIndex(t => t._id === data.ticket._id)
    if (index !== -1) {
      tickets.value[index] = data.ticket
    }
    if (currentTicket.value?._id === data.ticket._id) {
      currentTicket.value = data.ticket
    }
  })

  socket.on('ticket:assigned', data => {
    const index = tickets.value.findIndex(t => t._id === data.ticket._id)
    if (index !== -1) {
      tickets.value[index] = data.ticket
    }
    if (currentTicket.value?._id === data.ticket._id) {
      currentTicket.value = data.ticket
    }
  })

  socket.on('ticket:assigned-to-you', data => {
    toast.success(`You have been assigned to ticket: ${data.ticket.title}`)
  })

  socket.on('ticket:deleted', data => {
    tickets.value = tickets.value.filter(t => t._id !== data.ticketId)
  })

  return {
    // State
    tickets,
    currentTicket,
    loading,
    error,
    
    // Computed
    openTickets,
    inProgressTickets,
    resolvedTickets,
    closedTickets,
    myTickets,
    assignedTickets,
    
    // Actions
    $reset,
    setLoading,
    setError,
    getTickets,
    getTicket,
    createTicket,
    updateTicket,
    assignTicket,
    deleteTicket,
  }
}, {
  persistedState: {
    persist: true,
    paths: ['tickets', 'currentTicket'],
  },
})
