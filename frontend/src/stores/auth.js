import { defineStore } from 'pinia'
import { socket, updateSocketAuth } from '@socket/socket'
import { ref, computed } from 'vue'
import { toast } from 'vue3-toastify'

export const useSocketStore = defineStore('Socket', () => {
  const token = ref(null)
  const user = ref(null)
  const sessions = ref(null)
  const recentDevices = ref(null)
  const selectedNotification = ref(null)
  const notificationSettingsId = ref(null)
  const loading = ref(false)
  const error = ref(null)
  const isAuthenticated = ref(false)

  // Computed
  const userRole = computed(() => user.value?.role || 'user')
  const isAdmin = computed(() => userRole.value === 'admin')
  const isAgent = computed(() => userRole.value === 'agent')
  const isUser = computed(() => userRole.value === 'user')

  function $reset() {
    token.value = null
    user.value = null
    sessions.value = null
    recentDevices.value = null
    selectedNotification.value = null
    notificationSettingsId.value = null
    loading.value = false
    error.value = null
    isAuthenticated.value = false

    // Clear session storage
    sessionStorage.removeItem('accessToken')
    sessionStorage.removeItem('userData')

    // Clear user abilities
    localStorage.removeItem('userAbilities')
  }

  function setLoading(value) {
    loading.value = value
  }

  function setError(errorMessage) {
    error.value = errorMessage
    if (errorMessage) {
      toast.error(errorMessage)
    }
  }

  function setAuthData(authToken, userData) {
    console.log('Setting auth data:', { authToken, userData })
    token.value = authToken
    user.value = userData
    isAuthenticated.value = true

    // Store in session storage
    sessionStorage.setItem('accessToken', authToken)
    sessionStorage.setItem('userData', JSON.stringify(userData))

    // Set user abilities based on role
    if(userData.role === 'admin') {
      localStorage.setItem('userAbilities', JSON.stringify({ action: 'manage', subject: 'all' }))
    } else if(userData.role === 'agent') {
      localStorage.setItem('userAbilities', JSON.stringify([
        { action: 'manage', subject: 'all' },
        { action: 'read', subject: 'tickets' },
        { action: 'update', subject: 'tickets' },
        { action: 'create', subject: 'tickets' },
      ]))
      sessionStorage.setItem('userAbilities', JSON.stringify([
        { action: 'manage', subject: 'all' },
        { action: 'read', subject: 'tickets' },
        { action: 'update', subject: 'tickets' },
        { action: 'create', subject: 'tickets' },
      ]))
    } else {
      // Regular users
      localStorage.setItem('userAbilities', JSON.stringify([
        { action: 'read', subject: 'tickets', conditions: { user: { $eq: userData._id } } },
        { action: 'create', subject: 'tickets' },
      ]))
    }

    // Update socket authentication
    updateSocketAuth()

    console.log('Auth data set. isAuthenticated:', isAuthenticated.value)
  }

  function initializeAuth() {
    // Load from session storage on app start
    const storedToken = sessionStorage.getItem('accessToken')
    const storedUser = sessionStorage.getItem('userData')

    if (storedToken && storedUser) {
      try {
        token.value = storedToken
        user.value = JSON.parse(storedUser)
        isAuthenticated.value = true

        // Set user abilities based on role
        const userData = JSON.parse(storedUser)
        if(userData.role === 'admin') {
          localStorage.setItem('userAbilities', JSON.stringify({ action: 'manage', subject: 'all' }))
        } else if(userData.role === 'agent') {
          localStorage.setItem('userAbilities', JSON.stringify([
            { action: 'read', subject: 'tickets' },
            { action: 'update', subject: 'tickets' },
            { action: 'create', subject: 'tickets' },
          ]))
        } else {
          // Regular users
          localStorage.setItem('userAbilities', JSON.stringify([
            { action: 'read', subject: 'tickets', conditions: { user: { $eq: userData._id } } },
            { action: 'create', subject: 'tickets' },
          ]))
        }

        // Update socket authentication
        updateSocketAuth()
      } catch (err) {
        console.error('Failed to parse stored user data:', err)
        $reset()
      }
    }
  }

  function login(data) {
    console.log('Login function called with:', data)
    setLoading(true)
    socket.emit('authController:login', {
      email: data.email,
      password: data.password,
    })
    console.log('Login socket event emitted')
  }

  function register(data) {
    setLoading(true)
    console.log({ data })
    socket.emit('authController:register', {
      name: data.name,
      email: data.email,
      password: data.password,
      role: data.role || 'user',
      supportAuthId: data.supportAuthId,
    })
  }

  function verifyAccount(data) {
    socket.emit('authController:verifyAccount', data)
  }

  function resendVerifyAccount(data) {
    socket.emit('authController:resendVerifyAccount', data)
  }

  function logout(data) {
    setLoading(true)
    console.log('logout', { data })
    socket.emit('authController:logout', data)
  }

  function getMe(data) {
    socket.emit('authController:getMe', data)
  }

  function forgotPassword(data) {
    console.log('forgotPassword', { data })
    socket.emit('authController:forgotPassword', data)
  }

  function resetPassword(data) {
    console.log('reset', { data })
    socket.emit('authController:resetPassword', data)
  }

  function updateDetails(data) {
    socket.emit('authController:updateDetails', data)
  }

  function updatePassword(data) {
    socket.emit('authController:updatePassword', data)
  }

  function setup2Fa(data) {
    console.log('setup2Fa', { data })
    socket.emit('authController:setup2fa', data)
  }

  function verify2Fa(data) {
    console.log('verify2Fa', { data })
    socket.emit('authController:verify2fa', data)
  }

  function reset2Fa(data) {
    console.log('reset2Fa', { data })
    socket.emit('authController:reset2fa', data)
  }

  function setupOtp(data) {
    console.log('setupOtp', { data })
    socket.emit('authController:sendOTP', data)
  }

  function verifyOtp(data) {
    console.log('verifyOtp', { data })
    socket.emit('authController:verifyOTP', data)
  }

  function disableOtp(data) {
    console.log('verifyOtp', { data })
    socket.emit('authController:disableOtp', data)
  }

  function getAllSessions(data) {
    socket.emit('sessionController:getSessions', data)
  }

  function getSession(data) {
    socket.emit('sessionController:getSession', data)
  }

  function deleteSession(data) {
    socket.emit('sessionController:deleteSession', data)
  }

  function deleteAllSessions(data) {
    socket.emit('sessionController:deleteAllSessions', data)
  }

  function getNotificationSettings(data) {
    socket.emit('notificationSettingController:getNotificationSettings', data)
  }

  function createNotificationSettings(data) {
    socket.emit('notificationSettingController:createNotificationSetting', data)
  }

  function updateNotificationSettings(data) {
    socket.emit('notificationSettingController:updateNotificationSetting', data)
  }

  // Socket Listeners
  socket.on('authController:login', response => {
    console.log('Auth store received login response:', response)
    setLoading(false)
    if (response.status === 'success') {
      setAuthData(response.data.token, response.data.user)
      toast.success('🎉 Login successful!')
    } else {
      setError(response.data?.message || 'Login failed')
      toast.error(`❌ Login failed: ${response.data?.message || 'Unknown error'}`)
    }
  })

  socket.on('authController:register', response => {
    console.log('Auth store received register response:', response)
    setLoading(false)
    if (response.status === 'success') {
      setAuthData(response.data.token, response.data.user)
      toast.success('🎉 Registration successful!')
    } else {
      setError(response.data?.message || 'Registration failed')
      toast.error(`❌ Registration failed: ${response.data?.message || 'Unknown error'}`)
    }
  })

  socket.on('authController:logout', response => {
    setLoading(false)
    if (response.status === 'success') {
      $reset()
      toast.success('👋 Logged out successfully!')
    } else {
      setError(response.data?.message || 'Logout failed')
      toast.error(`❌ Logout failed: ${response.data?.message || 'Unknown error'}`)
    }
  })

  socket.on('authController:getMe', response => {
    if (response.status === 'success') {
      user.value = response.data
      sessionStorage.setItem('userData', JSON.stringify(response.data))
    } else if (response.status === 'error') {
      setError(response.data?.message || 'Failed to get user data')
    }
  })

  socket.on('authController:forgotPassword', response => {
    if (response.status === 'success') {
      toast.success('📧 Password reset email sent!')
    } else {
      setError(response.data?.message || 'Failed to send reset email')
      toast.error(`❌ Failed to send reset email: ${response.data?.message || 'Unknown error'}`)
    }
  })

  socket.on('authController:resetPassword', response => {
    if (response.status === 'success') {
      setAuthData(response.data.token, response.data.user)
      toast.success('🔐 Password reset successful!')
    } else {
      setError(response.data?.message || 'Password reset failed')
      toast.error(`❌ Password reset failed: ${response.data?.message || 'Unknown error'}`)
    }
  })

  socket.on('authController:updateDetails', response => {
    setLoading(false)
    if (response.status === 'success') {
      user.value = response.data
      sessionStorage.setItem('userData', JSON.stringify(response.data))
      toast.success('✏️ Profile updated successfully!')
    } else {
      setError(response.data?.message || 'Failed to update profile')
      toast.error(`❌ Failed to update profile: ${response.data?.message || 'Unknown error'}`)
    }
  })

  socket.on('authController:updatePassword', response => {
    setLoading(false)
    if (response.status === 'success') {
      toast.success('🔐 Password updated successfully!')
    } else {
      setError(response.data?.message || 'Failed to update password')
      toast.error(`❌ Failed to update password: ${response.data?.message || 'Unknown error'}`)
    }
  })

  // Initialize auth on store creation
  initializeAuth()

  return {
    // State
    token,
    user,
    sessions,
    recentDevices,
    selectedNotification,
    notificationSettingsId,
    loading,
    error,

    // Computed
    isAuthenticated,
    userRole,
    isAdmin,
    isAgent,
    isUser,

    // Actions
    $reset,
    setLoading,
    setError,
    setAuthData,
    initializeAuth,
    login,
    register,
    verifyAccount,
    resendVerifyAccount,
    logout,
    getMe,
    forgotPassword,
    resetPassword,
    updateDetails,
    updatePassword,
    setup2Fa,
    verify2Fa,
    reset2Fa,
    setupOtp,
    verifyOtp,
    disableOtp,
    getAllSessions,
    getSession,
    deleteSession,
    deleteAllSessions,
    getNotificationSettings,
    createNotificationSettings,
    updateNotificationSettings,
  }
}, {
  persistedState: {
    persist: true,
  },
})
