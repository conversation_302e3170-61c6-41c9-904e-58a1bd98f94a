<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useTicketStore } from '@stores/tickets'
import { useSocketStore } from '@stores/auth'
import TicketList from '@/components/tickets/TicketList.vue'
import { socket } from '@socketio/socket'
import { toast } from 'vue3-toastify'

definePage({
  meta: {
    layout: 'default',
  },
})

const ticketStore = useTicketStore()
const authStore = useSocketStore()

const showAssignDialog = ref(false)
const assigningTicket = ref(null)
const selectedAgent = ref('')

const user = computed(() => authStore.user)

const canViewAgentPanel = computed(() => {
  return user.value && ['admin', 'agent'].includes(user.value.role)
})

const unassignedTickets = computed(() => {
  return ticketStore.tickets.filter(ticket => !ticket.assignedAgent)
})

// Mock agents data - in real app, this would come from a users store
const agents = ref([
  { _id: '1', name: 'John <PERSON>', email: '<EMAIL>' },
  { _id: '2', name: '<PERSON>', email: '<EMAIL>' },
])

function handleRefresh() {
  ticketStore.getTickets()
}

function handleAssign(ticket) {
  assigningTicket.value = ticket
  selectedAgent.value = user.value._id // Default to current user
  showAssignDialog.value = true
}

function handleAssignSubmit() {
  if (assigningTicket.value) {
    ticketStore.assignTicket(assigningTicket.value._id, selectedAgent.value)
    showAssignDialog.value = false
    assigningTicket.value = null
    selectedAgent.value = ''
  }
}

function handleAssignCancel() {
  showAssignDialog.value = false
  assigningTicket.value = null
  selectedAgent.value = ''
}

function handleAssignToMe(ticket) {
  ticketStore.assignTicket(ticket._id, user.value._id)
}

function setupSocketListeners() {
  // Listen for new tickets that become unassigned
  socket.on('ticket:created', data => {
    if (!data.ticket.assignedAgent) {
      toast.info(`New unassigned ticket: ${data.ticket.title}`)
      ticketStore.getTickets() // Refresh tickets
    }
  })

  socket.on('ticket:assigned', data => {
    // Refresh to remove assigned tickets from unassigned list
    ticketStore.getTickets()
  })

  socket.on('ticket:updated', data => {
    // Refresh in case assignment status changed
    ticketStore.getTickets()
  })
}

function removeSocketListeners() {
  socket.off('ticket:created')
  socket.off('ticket:assigned')
  socket.off('ticket:updated')
}

onMounted(() => {
  if (!canViewAgentPanel.value) {
    toast.error('You do not have permission to access the agent panel')
    
    return
  }
  setupSocketListeners()
  ticketStore.getTickets()
})

onUnmounted(() => {
  removeSocketListeners()
})
</script>

<template>
  <div>
    <VRow>
      <VCol cols="12">
        <div class="d-flex justify-space-between align-center mb-6">
          <h1 class="text-h4">
            Unassigned Tickets
          </h1>
          <VBtn
            color="primary"
            prepend-icon="tabler-plus"
            @click="$router.push({ name: 'tickets-create' })"
          >
            Create Ticket
          </VBtn>
        </div>
      </VCol>
    </VRow>

    <VRow v-if="!canViewAgentPanel">
      <VCol cols="12">
        <VAlert
          type="warning"
          variant="tonal"
        >
          You do not have permission to access the agent panel.
        </VAlert>
      </VCol>
    </VRow>

    <VRow v-else>
      <VCol cols="12">
        <!-- Stats Card -->
        <VCard class="mb-6">
          <VCardText>
            <div class="d-flex align-center">
              <VIcon
                icon="tabler-user-question"
                size="40"
                color="warning"
                class="me-4"
              />
              <div>
                <h3 class="text-h5">
                  {{ unassignedTickets.length }}
                </h3>
                <p class="text-body-2 mb-0">
                  Tickets waiting for assignment
                </p>
              </div>
            </div>
          </VCardText>
        </VCard>

        <!-- Quick Actions -->
        <VCard
          v-if="unassignedTickets.length > 0"
          class="mb-6"
        >
          <VCardTitle>Quick Actions</VCardTitle>
          <VCardText>
            <div class="d-flex flex-wrap gap-4">
              <VBtn
                color="primary"
                prepend-icon="tabler-user-plus"
                @click="handleAssignToMe(unassignedTickets[0])"
              >
                Assign Next to Me
              </VBtn>
              
              <VBtn
                variant="outlined"
                prepend-icon="tabler-refresh"
                @click="handleRefresh"
              >
                Refresh
              </VBtn>
            </div>
          </VCardText>
        </VCard>

        <TicketList
          :tickets="unassignedTickets"
          :loading="ticketStore.loading"
          @refresh="handleRefresh"
          @assign="handleAssign"
        />
      </VCol>
    </VRow>

    <!-- Assign Dialog -->
    <VDialog
      v-model="showAssignDialog"
      max-width="500px"
      persistent
    >
      <VCard>
        <VCardTitle>Assign Ticket</VCardTitle>
        <VCardText>
          <p class="mb-4">
            Assign ticket "{{ assigningTicket?.title }}" to an agent:
          </p>
          
          <VSelect
            v-model="selectedAgent"
            :items="agents"
            item-title="name"
            item-value="_id"
            label="Select Agent"
            variant="outlined"
            required
          />

          <VAlert
            v-if="selectedAgent === user._id"
            type="info"
            variant="tonal"
            class="mt-4"
          >
            This ticket will be assigned to you.
          </VAlert>
        </VCardText>
        <VCardActions>
          <VSpacer />
          <VBtn
            variant="outlined"
            @click="handleAssignCancel"
          >
            Cancel
          </VBtn>
          <VBtn
            color="primary"
            :loading="ticketStore.loading"
            :disabled="!selectedAgent"
            @click="handleAssignSubmit"
          >
            Assign
          </VBtn>
        </VCardActions>
      </VCard>
    </VDialog>
  </div>
</template>
