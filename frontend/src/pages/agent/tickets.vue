<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useTicketStore } from '@stores/tickets'
import { useSocketStore } from '@stores/auth'
import TicketList from '@/components/tickets/TicketList.vue'
import TicketForm from '@/components/tickets/TicketForm.vue'
import { socket } from '@socket/socket'
import { toast } from 'vue3-toastify'

definePage({
  meta: {
    layout: 'default',
  },
})

const ticketStore = useTicketStore()
const authStore = useSocketStore()

const showEditDialog = ref(false)
const showAssignDialog = ref(false)
const editingTicket = ref(null)
const assigningTicket = ref(null)
const selectedAgent = ref('')

const user = computed(() => authStore.user)

const canViewAgentPanel = computed(() => {
  return user.value && ['admin', 'agent'].includes(user.value.role)
})

const assignedTickets = computed(() => {
  if (!user.value) return []
  
  return ticketStore.tickets.filter(ticket => ticket.assignedAgent?._id === user.value._id)
})

// Mock agents data - in real app, this would come from a users store
const agents = ref([
  { _id: '1', name: 'John Agent', email: '<EMAIL>' },
  { _id: '2', name: 'Jane Support', email: '<EMAIL>' },
])

function handleRefresh() {
  ticketStore.getTickets()
}

function handleEdit(ticket) {
  editingTicket.value = ticket
  showEditDialog.value = true
}

function handleEditSubmit(formData) {
  if (editingTicket.value) {
    ticketStore.updateTicket(editingTicket.value._id, formData)
    showEditDialog.value = false
    editingTicket.value = null
  }
}

function handleEditCancel() {
  showEditDialog.value = false
  editingTicket.value = null
}

function handleAssign(ticket) {
  assigningTicket.value = ticket
  selectedAgent.value = ticket.assignedAgent?._id || ''
  showAssignDialog.value = true
}

function handleAssignSubmit() {
  if (assigningTicket.value) {
    ticketStore.assignTicket(assigningTicket.value._id, selectedAgent.value)
    showAssignDialog.value = false
    assigningTicket.value = null
    selectedAgent.value = ''
  }
}

function handleAssignCancel() {
  showAssignDialog.value = false
  assigningTicket.value = null
  selectedAgent.value = ''
}

function setupSocketListeners() {
  // Listen for ticket assignments to current agent
  socket.on('ticket:assigned-to-you', data => {
    if (data.ticket.assignedAgent._id === user.value?._id) {
      toast.info(`New ticket assigned: ${data.ticket.title}`)
      ticketStore.getTickets() // Refresh tickets
    }
  })

  socket.on('ticket:updated', data => {
    if (data.ticket.assignedAgent?._id === user.value?._id) {
      ticketStore.getTickets() // Refresh tickets
    }
  })

  socket.on('ticket:created', data => {
    // Refresh to show new unassigned tickets
    ticketStore.getTickets()
  })
}

function removeSocketListeners() {
  socket.off('ticket:assigned-to-you')
  socket.off('ticket:updated')
  socket.off('ticket:created')
}

onMounted(() => {
  if (!canViewAgentPanel.value) {
    toast.error('You do not have permission to access the agent panel')
    
    return
  }
  setupSocketListeners()
  ticketStore.getTickets()
})

onUnmounted(() => {
  removeSocketListeners()
})
</script>

<template>
  <div>
    <VRow>
      <VCol cols="12">
        <div class="d-flex justify-space-between align-center mb-6">
          <h1 class="text-h4">
            My Assigned Tickets
          </h1>
          <VBtn
            color="primary"
            prepend-icon="tabler-plus"
            @click="$router.push({ name: 'tickets-create' })"
          >
            Create Ticket
          </VBtn>
        </div>
      </VCol>
    </VRow>

    <VRow v-if="!canViewAgentPanel">
      <VCol cols="12">
        <VAlert
          type="warning"
          variant="tonal"
        >
          You do not have permission to access the agent panel.
        </VAlert>
      </VCol>
    </VRow>

    <VRow v-else>
      <VCol cols="12">
        <!-- Stats Cards -->
        <VRow class="mb-6">
          <VCol
            cols="12"
            sm="6"
            md="3"
          >
            <VCard>
              <VCardText>
                <div class="d-flex align-center">
                  <VIcon
                    icon="tabler-user-check"
                    size="40"
                    color="primary"
                    class="me-4"
                  />
                  <div>
                    <h3 class="text-h5">
                      {{ assignedTickets.length }}
                    </h3>
                    <p class="text-body-2 mb-0">
                      Assigned to Me
                    </p>
                  </div>
                </div>
              </VCardText>
            </VCard>
          </VCol>

          <VCol
            cols="12"
            sm="6"
            md="3"
          >
            <VCard>
              <VCardText>
                <div class="d-flex align-center">
                  <VIcon
                    icon="tabler-clock"
                    size="40"
                    color="warning"
                    class="me-4"
                  />
                  <div>
                    <h3 class="text-h5">
                      {{ assignedTickets.filter(t => t.status === 'open').length }}
                    </h3>
                    <p class="text-body-2 mb-0">
                      Open
                    </p>
                  </div>
                </div>
              </VCardText>
            </VCard>
          </VCol>

          <VCol
            cols="12"
            sm="6"
            md="3"
          >
            <VCard>
              <VCardText>
                <div class="d-flex align-center">
                  <VIcon
                    icon="tabler-progress"
                    size="40"
                    color="info"
                    class="me-4"
                  />
                  <div>
                    <h3 class="text-h5">
                      {{ assignedTickets.filter(t => t.status === 'in-progress').length }}
                    </h3>
                    <p class="text-body-2 mb-0">
                      In Progress
                    </p>
                  </div>
                </div>
              </VCardText>
            </VCard>
          </VCol>

          <VCol
            cols="12"
            sm="6"
            md="3"
          >
            <VCard>
              <VCardText>
                <div class="d-flex align-center">
                  <VIcon
                    icon="tabler-check"
                    size="40"
                    color="success"
                    class="me-4"
                  />
                  <div>
                    <h3 class="text-h5">
                      {{ assignedTickets.filter(t => t.status === 'resolved').length }}
                    </h3>
                    <p class="text-body-2 mb-0">
                      Resolved
                    </p>
                  </div>
                </div>
              </VCardText>
            </VCard>
          </VCol>
        </VRow>

        <TicketList
          :tickets="assignedTickets"
          :loading="ticketStore.loading"
          @refresh="handleRefresh"
          @edit="handleEdit"
          @assign="handleAssign"
        />
      </VCol>
    </VRow>

    <!-- Edit Dialog -->
    <VDialog
      v-model="showEditDialog"
      max-width="800px"
      persistent
    >
      <TicketForm
        :ticket="editingTicket"
        :loading="ticketStore.loading"
        show-advanced-fields
        :agents="agents"
        @submit="handleEditSubmit"
        @cancel="handleEditCancel"
      />
    </VDialog>

    <!-- Assign Dialog -->
    <VDialog
      v-model="showAssignDialog"
      max-width="500px"
      persistent
    >
      <VCard>
        <VCardTitle>Reassign Ticket</VCardTitle>
        <VCardText>
          <p class="mb-4">
            Reassign ticket "{{ assigningTicket?.title }}" to another agent:
          </p>
          
          <VSelect
            v-model="selectedAgent"
            :items="agents"
            item-title="name"
            item-value="_id"
            label="Select Agent"
            variant="outlined"
            clearable
            placeholder="Unassigned"
          />
        </VCardText>
        <VCardActions>
          <VSpacer />
          <VBtn
            variant="outlined"
            @click="handleAssignCancel"
          >
            Cancel
          </VBtn>
          <VBtn
            color="primary"
            :loading="ticketStore.loading"
            @click="handleAssignSubmit"
          >
            Reassign
          </VBtn>
        </VCardActions>
      </VCard>
    </VDialog>
  </div>
</template>
