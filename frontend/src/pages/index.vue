<script setup>
import { ref, computed, onMounted, onUnmounted, watch } from 'vue'
import { useTicketStore } from '@stores/tickets'
import { useMessageStore } from '@stores/messages'
import { useCategoryStore } from '@stores/categories'
import { useSocketStore } from '@stores/auth'
import { socket } from '@socket/socket'

definePage({
  meta: {
    layout: 'default',
  },
})

const ticketStore = useTicketStore()
const messageStore = useMessageStore()
const categoryStore = useCategoryStore()
const authStore = useSocketStore()

const stats = ref({
  totalTickets: 0,
  openTickets: 0,
  inProgressTickets: 0,
  resolvedTickets: 0,
  unreadMessages: 0,
})

const user = computed(() => authStore.user)

const recentTickets = computed(() =>
  ticketStore.tickets.slice(0, 5),
)

const userRole = computed(() => user.value?.role || 'user')

const dashboardTitle = computed(() => {
  switch (userRole.value) {
  case 'admin':
    return 'Admin Dashboard'
  case 'agent':
    return 'Agent Dashboard'
  default:
    return 'Support Dashboard'
  }
})

function updateStats() {
  stats.value = {
    totalTickets: ticketStore.tickets.length,
    openTickets: ticketStore.openTickets.length,
    inProgressTickets: ticketStore.inProgressTickets.length,
    resolvedTickets: ticketStore.resolvedTickets.length,
    unreadMessages: messageStore.unreadCount,
  }
}

function getStatusColor(status) {
  const colors = {
    'open': 'warning',
    'in-progress': 'info',
    'pending': 'secondary',
    'resolved': 'success',
    'closed': 'surface',
  }
  
  return colors[status] || 'primary'
}

function formatStatus(status) {
  return status.split('-').map(word =>
    word.charAt(0).toUpperCase() + word.slice(1),
  ).join(' ')
}

// Socket listeners for real-time updates
function setupSocketListeners() {
  // Listen for real-time ticket updates
  socket.on('ticket:created', data => {
    updateStats()
  })

  socket.on('ticket:updated', data => {
    updateStats()
  })

  socket.on('ticket:assigned', data => {
    updateStats()
  })

  socket.on('message:created', data => {
    updateStats()
  })
}

function removeSocketListeners() {
  socket.off('ticket:created')
  socket.off('ticket:updated')
  socket.off('ticket:assigned')
  socket.off('message:created')
}

// Watch for changes in ticket data
watch(() => ticketStore.tickets, updateStats, { deep: true })
watch(() => messageStore.unreadCount, updateStats)

onMounted(() => {
  setupSocketListeners()
  ticketStore.getTickets()
  messageStore.getUnreadCount()
  categoryStore.getCategories()

  // Update stats when data changes
  updateStats()
})

onUnmounted(() => {
  removeSocketListeners()
})
</script>

<template>
  <div>
    <VRow>
      <VCol cols="12">
        <h1 class="text-h4 mb-6">
          {{ dashboardTitle }}
        </h1>
      </VCol>
    </VRow>

    <!-- Stats Cards -->
    <VRow>
      <VCol
        cols="12"
        sm="6"
        md="3"
      >
        <VCard>
          <VCardText>
            <div class="d-flex align-center">
              <VIcon
                icon="tabler-tickets"
                size="40"
                color="primary"
                class="me-4"
              />
              <div>
                <h3 class="text-h5">
                  {{ stats.totalTickets }}
                </h3>
                <p class="text-body-2 mb-0">
                  Total Tickets
                </p>
              </div>
            </div>
          </VCardText>
        </VCard>
      </VCol>

      <VCol
        cols="12"
        sm="6"
        md="3"
      >
        <VCard>
          <VCardText>
            <div class="d-flex align-center">
              <VIcon
                icon="tabler-clock"
                size="40"
                color="warning"
                class="me-4"
              />
              <div>
                <h3 class="text-h5">
                  {{ stats.openTickets }}
                </h3>
                <p class="text-body-2 mb-0">
                  Open Tickets
                </p>
              </div>
            </div>
          </VCardText>
        </VCard>
      </VCol>

      <VCol
        cols="12"
        sm="6"
        md="3"
      >
        <VCard>
          <VCardText>
            <div class="d-flex align-center">
              <VIcon
                icon="tabler-progress"
                size="40"
                color="info"
                class="me-4"
              />
              <div>
                <h3 class="text-h5">
                  {{ stats.inProgressTickets }}
                </h3>
                <p class="text-body-2 mb-0">
                  In Progress
                </p>
              </div>
            </div>
          </VCardText>
        </VCard>
      </VCol>

      <VCol
        cols="12"
        sm="6"
        md="3"
      >
        <VCard>
          <VCardText>
            <div class="d-flex align-center">
              <VIcon
                icon="tabler-message"
                size="40"
                color="success"
                class="me-4"
              />
              <div>
                <h3 class="text-h5">
                  {{ stats.unreadMessages }}
                </h3>
                <p class="text-body-2 mb-0">
                  Unread Messages
                </p>
              </div>
            </div>
          </VCardText>
        </VCard>
      </VCol>
    </VRow>

    <!-- Quick Actions -->
    <VRow class="mt-6">
      <VCol cols="12">
        <VCard>
          <VCardTitle>Quick Actions</VCardTitle>
          <VCardText>
            <div class="d-flex flex-wrap gap-4">
              <VBtn
                color="primary"
                prepend-icon="tabler-plus"
                @click="$router.push({ name: 'tickets-create' })"
              >
                Create Ticket
              </VBtn>

              <VBtn
                variant="outlined"
                prepend-icon="tabler-ticket"
                @click="$router.push({ name: 'tickets-my' })"
              >
                My Tickets
              </VBtn>

              <VBtn
                v-if="['agent', 'admin'].includes(userRole)"
                variant="outlined"
                prepend-icon="tabler-user-check"
                @click="$router.push({ name: 'agent-tickets' })"
              >
                Assigned Tickets
              </VBtn>

              <VBtn
                v-if="userRole === 'admin'"
                variant="outlined"
                prepend-icon="tabler-settings"
                @click="$router.push({ name: 'admin-categories' })"
              >
                Manage Categories
              </VBtn>
            </div>
          </VCardText>
        </VCard>
      </VCol>
    </VRow>

    <!-- Recent Tickets -->
    <VRow class="mt-6">
      <VCol cols="12">
        <VCard>
          <VCardTitle>Recent Tickets</VCardTitle>
          <VCardText>
            <div v-if="recentTickets.length === 0">
              <p class="text-center text-medium-emphasis py-8">
                No tickets found. Create your first ticket to get started!
              </p>
            </div>

            <div v-else>
              <VList>
                <VListItem
                  v-for="ticket in recentTickets"
                  :key="ticket._id"
                  @click="$router.push({ name: 'tickets-view', params: { id: ticket._id } })"
                >
                  <template #prepend>
                    <VIcon
                      :icon="ticket.category?.icon || 'tabler-help-circle'"
                      :color="ticket.category?.color || 'primary'"
                    />
                  </template>

                  <VListItemTitle>{{ ticket.title }}</VListItemTitle>
                  <VListItemSubtitle>
                    {{ ticket.category?.name }} • {{ ticket.priority }} priority
                  </VListItemSubtitle>

                  <template #append>
                    <VChip
                      :color="getStatusColor(ticket.status)"
                      size="small"
                      variant="tonal"
                    >
                      {{ formatStatus(ticket.status) }}
                    </VChip>
                  </template>
                </VListItem>
              </VList>
            </div>
          </VCardText>
        </VCard>
      </VCol>
    </VRow>
  </div>
</template>
