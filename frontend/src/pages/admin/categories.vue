<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useCategoryStore } from '@stores/categories'
import { useSocketStore } from '@stores/auth'
import { socket } from '@socket/socket'
import { toast } from 'vue3-toastify'

definePage({
  meta: {
    layout: 'default',
  },
})

const categoryStore = useCategoryStore()
const authStore = useSocketStore()

const showCreateDialog = ref(false)
const showEditDialog = ref(false)
const showDeleteDialog = ref(false)
const editingCategory = ref(null)
const deletingCategory = ref(null)

const form = ref({
  name: '',
  description: '',
  color: '#3498db',
  icon: 'mdi-help-circle',
  defaultPriority: 'medium',
  estimatedResolutionTime: 24,
  sortOrder: 0,
  isActive: true,
})

const user = computed(() => authStore.user)

const canManageCategories = computed(() => {
  return user.value && user.value.role === 'admin'
})

const priorityOptions = [
  { title: 'Low', value: 'low' },
  { title: 'Medium', value: 'medium' },
  { title: 'High', value: 'high' },
  { title: 'Urgent', value: 'urgent' },
]

const iconOptions = [
  { title: 'Help Circle', value: 'mdi-help-circle' },
  { title: 'Tools', value: 'mdi-tools' },
  { title: 'Credit Card', value: 'mdi-credit-card' },
  { title: 'Lightbulb', value: 'mdi-lightbulb' },
  { title: 'Bug', value: 'mdi-bug' },
  { title: 'Account Alert', value: 'mdi-account-alert' },
  { title: 'Settings', value: 'mdi-settings' },
  { title: 'Information', value: 'mdi-information' },
]

function resetForm() {
  form.value = {
    name: '',
    description: '',
    color: '#3498db',
    icon: 'mdi-help-circle',
    defaultPriority: 'medium',
    estimatedResolutionTime: 24,
    sortOrder: 0,
    isActive: true,
  }
}

function handleCreate() {
  resetForm()
  showCreateDialog.value = true
}

function handleCreateSubmit() {
  categoryStore.createCategory(form.value).then(result => {
    if (result) {
      showCreateDialog.value = false
      resetForm()
    }
  })
}

function handleCreateCancel() {
  showCreateDialog.value = false
  resetForm()
}

function handleEdit(category) {
  editingCategory.value = category
  form.value = {
    name: category.name,
    description: category.description || '',
    color: category.color,
    icon: category.icon,
    defaultPriority: category.defaultPriority,
    estimatedResolutionTime: category.estimatedResolutionTime,
    sortOrder: category.sortOrder,
    isActive: category.isActive,
  }
  showEditDialog.value = true
}

function handleEditSubmit() {
  if (editingCategory.value) {
    categoryStore.updateCategory(editingCategory.value._id, form.value).then(result => {
      if (result) {
        showEditDialog.value = false
        editingCategory.value = null
        resetForm()
      }
    })
  }
}

function handleEditCancel() {
  showEditDialog.value = false
  editingCategory.value = null
  resetForm()
}

function handleDelete(category) {
  deletingCategory.value = category
  showDeleteDialog.value = true
}

function handleDeleteConfirm() {
  if (deletingCategory.value) {
    categoryStore.deleteCategory(deletingCategory.value._id).then(result => {
      if (result) {
        showDeleteDialog.value = false
        deletingCategory.value = null
      }
    })
  }
}

function handleDeleteCancel() {
  showDeleteDialog.value = false
  deletingCategory.value = null
}

function setupSocketListeners() {
  // Listen for category updates from other admins
  socket.on('categoryController:createCategory', response => {
    if (response.status === 'success') {
      categoryStore.getCategories() // Refresh categories
    }
  })

  socket.on('categoryController:updateCategory', response => {
    if (response.status === 'success') {
      categoryStore.getCategories() // Refresh categories
    }
  })

  socket.on('categoryController:deleteCategory', response => {
    if (response.status === 'success') {
      categoryStore.getCategories() // Refresh categories
    }
  })
}

function removeSocketListeners() {
  socket.off('categoryController:createCategory')
  socket.off('categoryController:updateCategory')
  socket.off('categoryController:deleteCategory')
}

onMounted(() => {
  if (!canManageCategories.value) {
    toast.error('You do not have permission to manage categories')
    
    return
  }
  setupSocketListeners()
  categoryStore.getCategories()
})

onUnmounted(() => {
  removeSocketListeners()
})
</script>

<template>
  <div>
    <VRow>
      <VCol cols="12">
        <div class="d-flex justify-space-between align-center mb-6">
          <h1 class="text-h4">
            Manage Categories
          </h1>
          <VBtn
            v-if="canManageCategories"
            color="primary"
            prepend-icon="tabler-plus"
            @click="handleCreate"
          >
            Create Category
          </VBtn>
        </div>
      </VCol>
    </VRow>

    <VRow v-if="!canManageCategories">
      <VCol cols="12">
        <VAlert
          type="warning"
          variant="tonal"
        >
          You do not have permission to manage categories.
        </VAlert>
      </VCol>
    </VRow>

    <VRow v-else>
      <VCol cols="12">
        <!-- Loading State -->
        <div
          v-if="categoryStore.loading"
          class="text-center py-8"
        >
          <VProgressCircular
            indeterminate
            color="primary"
          />
          <p class="mt-4">
            Loading categories...
          </p>
        </div>

        <!-- Categories List -->
        <VCard v-else>
          <VCardTitle>Categories</VCardTitle>
          <VCardText>
            <VDataTable
              :items="categoryStore.sortedCategories"
              :headers="[
                { title: 'Name', key: 'name' },
                { title: 'Description', key: 'description' },
                { title: 'Priority', key: 'defaultPriority' },
                { title: 'Resolution Time', key: 'estimatedResolutionTime' },
                { title: 'Active', key: 'isActive' },
                { title: 'Actions', key: 'actions', sortable: false }
              ]"
              item-value="_id"
            >
              <template #item.name="{ item }">
                <div class="d-flex align-center">
                  <VIcon
                    :icon="item.icon"
                    :color="item.color"
                    class="me-2"
                  />
                  {{ item.name }}
                </div>
              </template>

              <template #item.defaultPriority="{ item }">
                <VChip
                  :color="getPriorityColor(item.defaultPriority)"
                  size="small"
                  variant="tonal"
                >
                  {{ item.defaultPriority }}
                </VChip>
              </template>

              <template #item.estimatedResolutionTime="{ item }">
                {{ item.estimatedResolutionTime }}h
              </template>

              <template #item.isActive="{ item }">
                <VChip
                  :color="item.isActive ? 'success' : 'error'"
                  size="small"
                  variant="tonal"
                >
                  {{ item.isActive ? 'Active' : 'Inactive' }}
                </VChip>
              </template>

              <template #item.actions="{ item }">
                <VBtn
                  icon="tabler-edit"
                  size="small"
                  variant="text"
                  @click="handleEdit(item)"
                />
                <VBtn
                  icon="tabler-trash"
                  size="small"
                  variant="text"
                  color="error"
                  @click="handleDelete(item)"
                />
              </template>
            </VDataTable>
          </VCardText>
        </VCard>
      </VCol>
    </VRow>

    <!-- Create/Edit Dialog -->
    <VDialog
      v-model="showCreateDialog"
      max-width="600px"
      persistent
    >
      <VCard>
        <VCardTitle>Create Category</VCardTitle>
        <VCardText>
          <VForm>
            <VRow>
              <VCol cols="12">
                <VTextField
                  v-model="form.name"
                  label="Name"
                  variant="outlined"
                  required
                />
              </VCol>

              <VCol cols="12">
                <VTextarea
                  v-model="form.description"
                  label="Description"
                  variant="outlined"
                  rows="3"
                />
              </VCol>

              <VCol
                cols="12"
                md="6"
              >
                <VSelect
                  v-model="form.icon"
                  :items="iconOptions"
                  label="Icon"
                  variant="outlined"
                />
              </VCol>

              <VCol
                cols="12"
                md="6"
              >
                <VTextField
                  v-model="form.color"
                  label="Color"
                  type="color"
                  variant="outlined"
                />
              </VCol>

              <VCol
                cols="12"
                md="6"
              >
                <VSelect
                  v-model="form.defaultPriority"
                  :items="priorityOptions"
                  label="Default Priority"
                  variant="outlined"
                />
              </VCol>

              <VCol
                cols="12"
                md="6"
              >
                <VTextField
                  v-model.number="form.estimatedResolutionTime"
                  label="Estimated Resolution Time (hours)"
                  type="number"
                  variant="outlined"
                />
              </VCol>

              <VCol cols="12">
                <VSwitch
                  v-model="form.isActive"
                  label="Active"
                />
              </VCol>
            </VRow>
          </VForm>
        </VCardText>
        <VCardActions>
          <VSpacer />
          <VBtn
            variant="outlined"
            @click="handleCreateCancel"
          >
            Cancel
          </VBtn>
          <VBtn
            color="primary"
            :loading="categoryStore.loading"
            @click="handleCreateSubmit"
          >
            Create
          </VBtn>
        </VCardActions>
      </VCard>
    </VDialog>

    <!-- Edit Dialog -->
    <VDialog
      v-model="showEditDialog"
      max-width="600px"
      persistent
    >
      <VCard>
        <VCardTitle>Edit Category</VCardTitle>
        <VCardText>
          <!-- Same form as create -->
          <VForm>
            <VRow>
              <VCol cols="12">
                <VTextField
                  v-model="form.name"
                  label="Name"
                  variant="outlined"
                  required
                />
              </VCol>

              <VCol cols="12">
                <VTextarea
                  v-model="form.description"
                  label="Description"
                  variant="outlined"
                  rows="3"
                />
              </VCol>

              <VCol
                cols="12"
                md="6"
              >
                <VSelect
                  v-model="form.icon"
                  :items="iconOptions"
                  label="Icon"
                  variant="outlined"
                />
              </VCol>

              <VCol
                cols="12"
                md="6"
              >
                <VTextField
                  v-model="form.color"
                  label="Color"
                  type="color"
                  variant="outlined"
                />
              </VCol>

              <VCol
                cols="12"
                md="6"
              >
                <VSelect
                  v-model="form.defaultPriority"
                  :items="priorityOptions"
                  label="Default Priority"
                  variant="outlined"
                />
              </VCol>

              <VCol
                cols="12"
                md="6"
              >
                <VTextField
                  v-model.number="form.estimatedResolutionTime"
                  label="Estimated Resolution Time (hours)"
                  type="number"
                  variant="outlined"
                />
              </VCol>

              <VCol cols="12">
                <VSwitch
                  v-model="form.isActive"
                  label="Active"
                />
              </VCol>
            </VRow>
          </VForm>
        </VCardText>
        <VCardActions>
          <VSpacer />
          <VBtn
            variant="outlined"
            @click="handleEditCancel"
          >
            Cancel
          </VBtn>
          <VBtn
            color="primary"
            :loading="categoryStore.loading"
            @click="handleEditSubmit"
          >
            Update
          </VBtn>
        </VCardActions>
      </VCard>
    </VDialog>

    <!-- Delete Dialog -->
    <VDialog
      v-model="showDeleteDialog"
      max-width="500px"
      persistent
    >
      <VCard>
        <VCardTitle>Delete Category</VCardTitle>
        <VCardText>
          <p>Are you sure you want to delete the category "{{ deletingCategory?.name }}"?</p>
          <p class="text-error">
            This action cannot be undone.
          </p>
        </VCardText>
        <VCardActions>
          <VSpacer />
          <VBtn
            variant="outlined"
            @click="handleDeleteCancel"
          >
            Cancel
          </VBtn>
          <VBtn
            color="error"
            :loading="categoryStore.loading"
            @click="handleDeleteConfirm"
          >
            Delete
          </VBtn>
        </VCardActions>
      </VCard>
    </VDialog>
  </div>
</template>

<script>
function getPriorityColor(priority) {
  const colors = {
    'low': 'success',
    'medium': 'warning',
    'high': 'error',
    'urgent': 'error',
  }
  
  return colors[priority] || 'primary'
}
</script>
