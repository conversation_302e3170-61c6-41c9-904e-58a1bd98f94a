<script setup>
import { ref, watch, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { useSocketStore } from '@stores/auth'
import { socket } from '@socket/socket'
import AuthProvider from '@/views/pages/authentication/AuthProvider.vue'
import { useGenerateImageVariant } from '@core/composable/useGenerateImageVariant'
import authV2RegisterIllustrationBorderedDark from '@images/pages/auth-v2-register-illustration-bordered-dark.png'
import authV2RegisterIllustrationBorderedLight from '@images/pages/auth-v2-register-illustration-bordered-light.png'
import authV2RegisterIllustrationDark from '@images/pages/auth-v2-register-illustration-dark.png'
import authV2RegisterIllustrationLight from '@images/pages/auth-v2-register-illustration-light.png'
import authV2MaskDark from '@images/pages/misc-mask-dark.png'
import authV2MaskLight from '@images/pages/misc-mask-light.png'
import { VNodeRenderer } from '@layouts/components/VNodeRenderer'
import { themeConfig } from '@themeConfig'

definePage({
  meta: {
    layout: 'blank',
    public: true,
  },
})

const router = useRouter()
const authStore = useSocketStore()

const form = ref({
  name: '',
  email: '',
  password: '',
  confirmPassword: '',
  role: 'user',
  supportAuthId: '',
  privacyPolicies: false,
})

const isPasswordVisible = ref(false)
const isConfirmPasswordVisible = ref(false)
const authThemeImg = useGenerateImageVariant(authV2RegisterIllustrationLight, authV2RegisterIllustrationDark, authV2RegisterIllustrationBorderedLight, authV2RegisterIllustrationBorderedDark, true)
const authThemeMask = useGenerateImageVariant(authV2MaskLight, authV2MaskDark)

const roleOptions = [
  { title: 'User', value: 'user' },
  { title: 'Agent', value: 'agent' },
  { title: 'Admin', value: 'admin' },
]

function handleRegister() {
  if (form.value.password !== form.value.confirmPassword) {
    alert('Passwords do not match')
    
    return
  }

  if (!form.value.privacyPolicies) {
    alert('Please accept the privacy policies')
    
    return
  }

  if (!form.value.supportAuthId.includes('support-auth')) {
    alert('Support Auth ID must contain "support-auth"')
    
    return
  }

  if (form.value.name && form.value.email && form.value.password && form.value.supportAuthId) {
    authStore.register({
      name: form.value.name,
      email: form.value.email,
      password: form.value.password,
      role: form.value.role,
      supportAuthId: form.value.supportAuthId,
    })
  }
}

// Watch for successful authentication
watch(() => authStore.isAuthenticated, isAuth => {
  console.log('Register - Auth state changed:', isAuth, 'Token:', authStore.token, 'User:', authStore.user)
  if (isAuth) {
    console.log('User registered and authenticated, redirecting...')
    router.push('/')
  }
}, { immediate: true })

// Check if already authenticated on mount
onMounted(() => {
  console.log('Register page mounted. Current auth state:', authStore.isAuthenticated)
  if (authStore.isAuthenticated) {
    console.log('Already authenticated, redirecting...')
    router.push('/')
  }
})
</script>

<template>
  <VRow
    no-gutters
    class="auth-wrapper bg-surface"
  >
    <VCol
      md="8"
      class="d-none d-md-flex"
    >
      <div class="position-relative bg-background w-100 me-0">
        <div
          class="d-flex align-center justify-center w-100 h-100"
          style="padding-inline: 6.25rem;"
        >
          <VImg
            max-width="613"
            :src="authThemeImg"
            class="auth-illustration mt-16 mb-2"
          />
        </div>

        <VImg
          class="auth-footer-mask"
          :src="authThemeMask"
        />
      </div>
    </VCol>

    <VCol
      cols="12"
      md="4"
      class="auth-card-v2 d-flex align-center justify-center"
    >
      <VCard
        flat
        :max-width="500"
        class="mt-12 mt-sm-0 pa-4"
      >
        <VCardText>
          <VNodeRenderer
            :nodes="themeConfig.app.logo"
            class="mb-6"
          />
          <h5 class="text-h5 mb-1">
            Adventure starts here 🚀
          </h5>
          <p class="mb-0">
            Make your app management easy and fun!
          </p>
        </VCardText>

        <VCardText>
          <VForm @submit.prevent="handleRegister">
            <VRow>
              <!-- Name -->
              <VCol cols="12">
                <VTextField
                  v-model="form.name"
                  autofocus
                  label="Full Name"
                  placeholder="Enter your full name"
                  :rules="[v => !!v || 'Name is required']"
                  required
                />
              </VCol>

              <!-- Email -->
              <VCol cols="12">
                <VTextField
                  v-model="form.email"
                  label="Email"
                  placeholder="Enter your email"
                  type="email"
                  :rules="[
                    v => !!v || 'Email is required',
                    v => /.+@.+\..+/.test(v) || 'Email must be valid'
                  ]"
                  required
                />
              </VCol>

              <!-- Role -->
              <VCol cols="12">
                <VSelect
                  v-model="form.role"
                  :items="roleOptions"
                  label="Role"
                  placeholder="Select your role"
                />
              </VCol>

              <!-- Support Auth ID -->
              <VCol cols="12">
                <VTextField
                  v-model="form.supportAuthId"
                  label="Support Auth ID"
                  placeholder="Enter your support auth ID (must contain 'support-auth')"
                  :rules="[
                    v => !!v || 'Support Auth ID is required',
                    v => v.includes('support-auth') || 'Support Auth ID must contain support-auth'
                  ]"
                  required
                />
              </VCol>

              <!-- Password -->
              <VCol cols="12">
                <VTextField
                  v-model="form.password"
                  label="Password"
                  placeholder="Enter your password"
                  :type="isPasswordVisible ? 'text' : 'password'"
                  :append-inner-icon="isPasswordVisible ? 'tabler-eye-off' : 'tabler-eye'"
                  :rules="[
                    v => !!v || 'Password is required',
                    v => v.length >= 6 || 'Password must be at least 6 characters'
                  ]"
                  required
                  @click:append-inner="isPasswordVisible = !isPasswordVisible"
                />
              </VCol>

              <!-- Confirm Password -->
              <VCol cols="12">
                <VTextField
                  v-model="form.confirmPassword"
                  label="Confirm Password"
                  placeholder="Confirm your password"
                  :type="isConfirmPasswordVisible ? 'text' : 'password'"
                  :append-inner-icon="isConfirmPasswordVisible ? 'tabler-eye-off' : 'tabler-eye'"
                  :rules="[
                    v => !!v || 'Confirm password is required',
                    v => v === form.password || 'Passwords do not match'
                  ]"
                  required
                  @click:append-inner="isConfirmPasswordVisible = !isConfirmPasswordVisible"
                />
              </VCol>

              <!-- Privacy Policy -->
              <VCol cols="12">
                <div class="d-flex align-center mt-1 mb-4">
                  <VCheckbox
                    id="privacy-policy"
                    v-model="form.privacyPolicies"
                    inline
                  />
                  <VLabel
                    for="privacy-policy"
                    style="opacity: 1;"
                  >
                    <span class="me-1">I agree to</span>
                    <a
                      href="javascript:void(0)"
                      class="text-primary"
                    >privacy policy & terms</a>
                  </VLabel>
                </div>

                <VBtn
                  block
                  type="submit"
                  :loading="authStore.loading"
                  :disabled="!form.name || !form.email || !form.password || !form.confirmPassword || !form.supportAuthId || !form.privacyPolicies"
                >
                  Sign up
                </VBtn>
              </VCol>

              <!-- Create account -->
              <VCol
                cols="12"
                class="text-center text-base"
              >
                <span>Already have an account?</span>
                <RouterLink
                  class="text-primary ms-2"
                  :to="{ name: 'login' }"
                >
                  Sign in instead
                </RouterLink>
              </VCol>

              <VCol
                cols="12"
                class="d-flex align-center"
              >
                <VDivider />
                <span class="mx-4">or</span>
                <VDivider />
              </VCol>

              <!-- Auth providers -->
              <VCol
                cols="12"
                class="text-center"
              >
                <AuthProvider />
              </VCol>
            </VRow>
          </VForm>
        </VCardText>
      </VCard>
    </VCol>
  </VRow>
</template>

<style lang="scss">
@use "@core/scss/template/pages/page-auth.scss";
</style>
