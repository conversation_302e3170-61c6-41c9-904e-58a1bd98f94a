<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useTicketStore } from '@stores/tickets'
import { useSocketStore } from '@stores/auth'
import TicketList from '@/components/tickets/TicketList.vue'
import TicketForm from '@/components/tickets/TicketForm.vue'
import { socket } from '@socket/socket'

definePage({
  meta: {
    layout: 'default',
  },
})

const ticketStore = useTicketStore()
const authStore = useSocketStore()

const showEditDialog = ref(false)
const editingTicket = ref(null)

const user = computed(() => authStore.user)

const myTickets = computed(() => {
  if (!user.value) return []

  return Array.isArray(ticketStore.tickets) ? ticketStore.tickets.filter(ticket => ticket.user._id === user.value._id) : []
})

function handleRefresh() {
  ticketStore.getTickets()
}

function handleEdit(ticket) {
  editingTicket.value = ticket
  showEditDialog.value = true
}

function handleEditSubmit(formData) {
  if (editingTicket.value) {
    ticketStore.updateTicket(editingTicket.value._id, formData)
    showEditDialog.value = false
    editingTicket.value = null
  }
}

function handleEditCancel() {
  showEditDialog.value = false
  editingTicket.value = null
}

function setupSocketListeners() {
  // Listen for ticket updates that affect user's tickets
  socket.on('ticket:created', data => {
    if (data.ticket.user._id === user.value?._id) {
      ticketStore.getTickets() // Refresh tickets
    }
  })

  socket.on('ticket:updated', data => {
    if (data.ticket.user._id === user.value?._id) {
      ticketStore.getTickets() // Refresh tickets
    }
  })

  socket.on('ticket:assigned', data => {
    if (data.ticket.user._id === user.value?._id) {
      ticketStore.getTickets() // Refresh tickets
    }
  })
}

function removeSocketListeners() {
  socket.off('ticket:created')
  socket.off('ticket:updated')
  socket.off('ticket:assigned')
}

onMounted(() => {
  setupSocketListeners()
  ticketStore.getTickets()
})

onUnmounted(() => {
  removeSocketListeners()
})
</script>

<template>
  <div>
    <VRow>
      <VCol cols="12">
        <div class="d-flex justify-space-between align-center mb-6">
          <h1 class="text-h4">
            My Tickets
          </h1>
          <VBtn
            color="primary"
            prepend-icon="tabler-plus"
            @click="$router.push({ name: 'tickets-create' })"
          >
            Create Ticket
          </VBtn>
        </div>
      </VCol>
    </VRow>

    <VRow>
      <VCol cols="12">
        <TicketList
          :tickets="myTickets"
          :loading="ticketStore.loading"
          @refresh="handleRefresh"
          @edit="handleEdit"
        />
      </VCol>
    </VRow>

    <!-- Edit Dialog -->
    <VDialog
      v-model="showEditDialog"
      max-width="800px"
      persistent
    >
      <TicketForm
        :ticket="editingTicket"
        :loading="ticketStore.loading"
        @submit="handleEditSubmit"
        @cancel="handleEditCancel"
      />
    </VDialog>
  </div>
</template>
