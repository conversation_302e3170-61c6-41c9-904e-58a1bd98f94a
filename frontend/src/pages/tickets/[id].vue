<script setup>
import { ref, computed, onMounted, onUnmounted, nextTick } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useTicketStore } from '@stores/tickets'
import { useMessageStore } from '@stores/messages'
import { useSocketStore } from '@stores/auth'
import MessageItem from '@/components/chat/MessageItem.vue'
import ChatInput from '@/components/chat/ChatInput.vue'
import { socket } from '@socketio/socket'
import moment from 'moment'

definePage({
  meta: {
    layout: 'default',
  },
})

const route = useRoute()
const router = useRouter()
const ticketStore = useTicketStore()
const messageStore = useMessageStore()
const authStore = useSocketStore()

const messagesContainer = ref()
const showEditDialog = ref(false)
const editingMessage = ref(null)
const editMessageText = ref('')

const ticketId = computed(() => route.params.id)
const ticket = computed(() => ticketStore.currentTicket)
const messages = computed(() => messageStore.sortedMessages)
const user = computed(() => authStore.user)

const canEdit = computed(() => {
  if (!user.value || !ticket.value) return false
  
  return user.value.role === 'admin' || 
         user.value.role === 'agent' || 
         ticket.value.user._id === user.value._id
})

const canAssign = computed(() => {
  if (!user.value) return false
  
  return user.value.role === 'admin' || user.value.role === 'agent'
})

function scrollToBottom() {
  nextTick(() => {
    if (messagesContainer.value) {
      messagesContainer.value.scrollTop = messagesContainer.value.scrollHeight
    }
  })
}

function handleSendMessage(messageData, files) {
  messageStore.createMessage(ticketId.value, messageData)
  scrollToBottom()
}

function handleStartTyping() {
  messageStore.startTyping(ticketId.value)
}

function handleStopTyping() {
  messageStore.stopTyping(ticketId.value)
}

function handleEditMessage(message) {
  editingMessage.value = message
  editMessageText.value = message.content
  showEditDialog.value = true
}

function handleEditSubmit() {
  if (editingMessage.value && editMessageText.value.trim()) {
    messageStore.updateMessage(editingMessage.value._id, {
      content: editMessageText.value.trim(),
    })
    showEditDialog.value = false
    editingMessage.value = null
    editMessageText.value = ''
  }
}

function handleEditCancel() {
  showEditDialog.value = false
  editingMessage.value = null
  editMessageText.value = ''
}

function handleDeleteMessage(message) {
  if (confirm('Are you sure you want to delete this message?')) {
    messageStore.deleteMessage(message._id)
  }
}

function getStatusColor(status) {
  const colors = {
    'open': 'warning',
    'in-progress': 'info',
    'pending': 'secondary',
    'resolved': 'success',
    'closed': 'surface',
  }
  
  return colors[status] || 'primary'
}

function formatStatus(status) {
  return status.split('-').map(word => 
    word.charAt(0).toUpperCase() + word.slice(1),
  ).join(' ')
}

function formatDate(date) {
  return moment(date).format('MMMM D, YYYY [at] h:mm A')
}

function getPriorityColor(priority) {
  const colors = {
    'low': 'success',
    'medium': 'warning',
    'high': 'error',
    'urgent': 'error',
  }
  
  return colors[priority] || 'primary'
}

function setupSocketListeners() {
  // Listen for ticket-specific events
  socket.on('ticket:updated', data => {
    if (data.ticket._id === ticketId.value) {
      ticketStore.currentTicket = data.ticket
    }
  })

  socket.on('ticket:assigned', data => {
    if (data.ticket._id === ticketId.value) {
      ticketStore.currentTicket = data.ticket
    }
  })

  socket.on('message:created', data => {
    if (data.ticketId === ticketId.value) {
      scrollToBottom()
    }
  })

  socket.on('attachment:uploaded', data => {
    if (data.attachment.ticket === ticketId.value) {
      scrollToBottom()
    }
  })
}

function removeSocketListeners() {
  socket.off('ticket:updated')
  socket.off('ticket:assigned')
  socket.off('message:created')
  socket.off('attachment:uploaded')
}

onMounted(() => {
  setupSocketListeners()
  ticketStore.getTicket(ticketId.value)
  messageStore.joinTicketRoom(ticketId.value)
  messageStore.getMessages(ticketId.value)

  // Scroll to bottom when messages load
  setTimeout(scrollToBottom, 500)
})

onUnmounted(() => {
  removeSocketListeners()
  messageStore.leaveTicketRoom(ticketId.value)
})
</script>

<template>
  <div v-if="ticket">
    <VRow>
      <VCol cols="12">
        <div class="d-flex align-center mb-6">
          <VBtn
            icon="tabler-arrow-left"
            variant="text"
            class="me-3"
            @click="router.back()"
          />
          <h1 class="text-h4">
            {{ ticket.title }}
          </h1>
        </div>
      </VCol>
    </VRow>

    <VRow>
      <!-- Ticket Details Sidebar -->
      <VCol
        cols="12"
        md="4"
        lg="3"
      >
        <VCard class="mb-6">
          <VCardTitle>Ticket Details</VCardTitle>
          <VCardText>
            <VList>
              <VListItem>
                <VListItemTitle>Status</VListItemTitle>
                <VListItemSubtitle>
                  <VChip
                    :color="getStatusColor(ticket.status)"
                    size="small"
                    variant="tonal"
                  >
                    {{ formatStatus(ticket.status) }}
                  </VChip>
                </VListItemSubtitle>
              </VListItem>

              <VListItem>
                <VListItemTitle>Priority</VListItemTitle>
                <VListItemSubtitle>
                  <VChip
                    :color="getPriorityColor(ticket.priority)"
                    size="small"
                    variant="tonal"
                  >
                    {{ ticket.priority }}
                  </VChip>
                </VListItemSubtitle>
              </VListItem>

              <VListItem>
                <VListItemTitle>Category</VListItemTitle>
                <VListItemSubtitle>
                  <div class="d-flex align-center">
                    <VIcon
                      :icon="ticket.category?.icon"
                      :color="ticket.category?.color"
                      size="16"
                      class="me-1"
                    />
                    {{ ticket.category?.name }}
                  </div>
                </VListItemSubtitle>
              </VListItem>

              <VListItem>
                <VListItemTitle>Created by</VListItemTitle>
                <VListItemSubtitle>{{ ticket.user?.name }}</VListItemSubtitle>
              </VListItem>

              <VListItem>
                <VListItemTitle>Created</VListItemTitle>
                <VListItemSubtitle>{{ formatDate(ticket.createdAt) }}</VListItemSubtitle>
              </VListItem>

              <VListItem v-if="ticket.assignedAgent">
                <VListItemTitle>Assigned to</VListItemTitle>
                <VListItemSubtitle>{{ ticket.assignedAgent.name }}</VListItemSubtitle>
              </VListItem>

              <VListItem v-if="ticket.lastActivity">
                <VListItemTitle>Last Activity</VListItemTitle>
                <VListItemSubtitle>{{ formatDate(ticket.lastActivity) }}</VListItemSubtitle>
              </VListItem>
            </VList>
          </VCardText>
        </VCard>

        <VCard>
          <VCardTitle>Description</VCardTitle>
          <VCardText>
            <p>{{ ticket.description }}</p>
          </VCardText>
        </VCard>
      </VCol>

      <!-- Chat Area -->
      <VCol
        cols="12"
        md="8"
        lg="9"
      >
        <VCard class="chat-container">
          <VCardTitle>
            <div class="d-flex justify-space-between align-center">
              <span>Conversation</span>
              <VChip
                v-if="messageStore.typingUsers.length > 0"
                color="info"
                size="small"
                variant="tonal"
              >
                <VIcon
                  icon="tabler-dots"
                  size="16"
                  class="me-1 typing-animation"
                />
                Someone is typing...
              </VChip>
            </div>
          </VCardTitle>

          <!-- Messages -->
          <VCardText
            ref="messagesContainer"
            class="messages-container"
          >
            <div v-if="messages.length === 0">
              <p class="text-center text-medium-emphasis py-8">
                No messages yet. Start the conversation!
              </p>
            </div>

            <div v-else>
              <MessageItem
                v-for="message in messages"
                :key="message._id"
                :message="message"
                @edit="handleEditMessage"
                @delete="handleDeleteMessage"
              />
            </div>
          </VCardText>

          <!-- Chat Input -->
          <div class="chat-input-wrapper">
            <ChatInput
              :ticket-id="ticketId"
              :typing-users="messageStore.typingUsers"
              :sending="messageStore.loading"
              @sendMessage="handleSendMessage"
              @startTyping="handleStartTyping"
              @stopTyping="handleStopTyping"
            />
          </div>
        </VCard>
      </VCol>
    </VRow>

    <!-- Edit Message Dialog -->
    <VDialog
      v-model="showEditDialog"
      max-width="600px"
      persistent
    >
      <VCard>
        <VCardTitle>Edit Message</VCardTitle>
        <VCardText>
          <VTextarea
            v-model="editMessageText"
            label="Message"
            variant="outlined"
            rows="4"
            auto-grow
          />
        </VCardText>
        <VCardActions>
          <VSpacer />
          <VBtn
            variant="outlined"
            @click="handleEditCancel"
          >
            Cancel
          </VBtn>
          <VBtn
            color="primary"
            :disabled="!editMessageText.trim()"
            @click="handleEditSubmit"
          >
            Update
          </VBtn>
        </VCardActions>
      </VCard>
    </VDialog>
  </div>

  <!-- Loading State -->
  <div
    v-else
    class="text-center py-8"
  >
    <VProgressCircular
      indeterminate
      color="primary"
    />
    <p class="mt-4">
      Loading ticket...
    </p>
  </div>
</template>

<style scoped>
.chat-container {
  height: calc(100vh - 200px);
  display: flex;
  flex-direction: column;
}

.messages-container {
  flex: 1;
  overflow-y: auto;
  max-height: calc(100vh - 400px);
}

.chat-input-wrapper {
  border-top: 1px solid rgb(var(--v-theme-surface-variant));
}

.typing-animation {
  animation: pulse 1.5s ease-in-out infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}
</style>
