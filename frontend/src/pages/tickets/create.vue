<script setup>
import { ref, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { useTicketStore } from '@stores/tickets'
import { useCategoryStore } from '@stores/categories'
import TicketForm from '@/components/tickets/TicketForm.vue'
import { socket } from '@socket/socket'
import { toast } from 'vue3-toastify'
import 'vue3-toastify/dist/index.css'

definePage({
  meta: {
    layout: 'default',
  },
})

const router = useRouter()
const ticketStore = useTicketStore()
const categoryStore = useCategoryStore()

const formRef = ref()

const isSubmitting = ref(false)

function handleSubmit(formData) {
  if (isSubmitting.value) return

  isSubmitting.value = true
  ticketStore.createTicket(formData)
}

// Socket listeners for ticket creation
function setupSocketListeners() {
  socket.on('ticketController:createTicket', response => {
    isSubmitting.value = false

    if (response.success) {
      toast.success('Ticket created successfully!')
      router.push({ name: 'tickets-my' })
    } else {
      toast.error(response.message || 'Failed to create ticket')
    }
  })
}

function removeSocketListeners() {
  socket.off('ticketController:createTicket')
}

function handleCancel() {
  router.back()
}

onMounted(() => {
  setupSocketListeners()
  categoryStore.getCategories()
})

onUnmounted(() => {
  removeSocketListeners()
})
</script>

<template>
  <div>
    <VRow>
      <VCol cols="12">
        <div class="d-flex align-center mb-6">
          <VBtn
            icon="tabler-arrow-left"
            variant="text"
            class="me-3"
            @click="handleCancel"
          />
          <h1 class="text-h4">
            Create New Ticket
          </h1>
        </div>
      </VCol>
    </VRow>

    <VRow>
      <VCol
        cols="12"
        md="8"
        lg="6"
      >
        <TicketForm
          ref="formRef"
          @submit="handleSubmit"
          @cancel="handleCancel"
        />
      </VCol>

      <VCol
        cols="12"
        md="4"
        lg="6"
      >
        <VCard>
          <VCardTitle>
            <VIcon
              icon="tabler-info-circle"
              class="me-2"
            />
            Tips for Creating a Good Ticket
          </VCardTitle>
          <VCardText>
            <VList>
              <VListItem>
                <template #prepend>
                  <VIcon
                    icon="tabler-check"
                    color="success"
                    size="20"
                  />
                </template>
                <VListItemTitle>Use a clear, descriptive title</VListItemTitle>
                <VListItemSubtitle>
                  Help us understand your issue at a glance
                </VListItemSubtitle>
              </VListItem>

              <VListItem>
                <template #prepend>
                  <VIcon
                    icon="tabler-check"
                    color="success"
                    size="20"
                  />
                </template>
                <VListItemTitle>Provide detailed description</VListItemTitle>
                <VListItemSubtitle>
                  Include steps to reproduce, expected vs actual behavior
                </VListItemSubtitle>
              </VListItem>

              <VListItem>
                <template #prepend>
                  <VIcon
                    icon="tabler-check"
                    color="success"
                    size="20"
                  />
                </template>
                <VListItemTitle>Choose the right category</VListItemTitle>
                <VListItemSubtitle>
                  This helps route your ticket to the right team
                </VListItemSubtitle>
              </VListItem>

              <VListItem>
                <template #prepend>
                  <VIcon
                    icon="tabler-check"
                    color="success"
                    size="20"
                  />
                </template>
                <VListItemTitle>Set appropriate priority</VListItemTitle>
                <VListItemSubtitle>
                  Urgent: System down, High: Major feature broken, Medium: Minor issues, Low: Enhancements
                </VListItemSubtitle>
              </VListItem>
            </VList>
          </VCardText>
        </VCard>

        <VCard class="mt-6">
          <VCardTitle>
            <VIcon
              icon="tabler-clock"
              class="me-2"
            />
            Response Times
          </VCardTitle>
          <VCardText>
            <VList>
              <VListItem>
                <template #prepend>
                  <VChip
                    color="error"
                    size="small"
                    variant="dot"
                  />
                </template>
                <VListItemTitle>Urgent</VListItemTitle>
                <VListItemSubtitle>Within 1 hour</VListItemSubtitle>
              </VListItem>

              <VListItem>
                <template #prepend>
                  <VChip
                    color="warning"
                    size="small"
                    variant="dot"
                  />
                </template>
                <VListItemTitle>High</VListItemTitle>
                <VListItemSubtitle>Within 4 hours</VListItemSubtitle>
              </VListItem>

              <VListItem>
                <template #prepend>
                  <VChip
                    color="info"
                    size="small"
                    variant="dot"
                  />
                </template>
                <VListItemTitle>Medium</VListItemTitle>
                <VListItemSubtitle>Within 24 hours</VListItemSubtitle>
              </VListItem>

              <VListItem>
                <template #prepend>
                  <VChip
                    color="success"
                    size="small"
                    variant="dot"
                  />
                </template>
                <VListItemTitle>Low</VListItemTitle>
                <VListItemSubtitle>Within 72 hours</VListItemSubtitle>
              </VListItem>
            </VList>
          </VCardText>
        </VCard>
      </VCol>
    </VRow>
  </div>
</template>
