<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useTicketStore } from '@stores/tickets'
import { useSocketStore } from '@stores/auth'
import TicketList from '@/components/tickets/TicketList.vue'
import TicketForm from '@/components/tickets/TicketForm.vue'
import { socket } from '@socket/socket'
import { toast } from 'vue3-toastify'

definePage({
  meta: {
    layout: 'default',
  },
})

const ticketStore = useTicketStore()
const authStore = useSocketStore()

const showEditDialog = ref(false)
const showAssignDialog = ref(false)
const showDeleteDialog = ref(false)
const editingTicket = ref(null)
const assigningTicket = ref(null)
const deletingTicket = ref(null)
const selectedAgent = ref('')

const user = computed(() => authStore.user)

const canViewAllTickets = computed(() => {
  return user.value && ['admin', 'agent'].includes(user.value.role)
})

// Mock agents data - in real app, this would come from a users store
const agents = ref([
  { _id: '1', name: 'John Agent', email: '<EMAIL>' },
  { _id: '2', name: 'Jane Support', email: '<EMAIL>' },
])

function handleRefresh() {
  ticketStore.getTickets()
}

function handleEdit(ticket) {
  editingTicket.value = ticket
  showEditDialog.value = true
}

function handleEditSubmit(formData) {
  if (editingTicket.value) {
    ticketStore.updateTicket(editingTicket.value._id, formData)
  }
}

function handleEditCancel() {
  showEditDialog.value = false
  editingTicket.value = null
}

function handleAssign(ticket) {
  assigningTicket.value = ticket
  selectedAgent.value = ticket.assignedAgent?._id || ''
  showAssignDialog.value = true
}

function handleAssignSubmit() {
  if (assigningTicket.value) {
    ticketStore.assignTicket(assigningTicket.value._id, selectedAgent.value)
  }
}

function handleAssignCancel() {
  showAssignDialog.value = false
  assigningTicket.value = null
  selectedAgent.value = ''
}

function handleDelete(ticket) {
  deletingTicket.value = ticket
  showDeleteDialog.value = true
}

function handleDeleteConfirm() {
  if (deletingTicket.value) {
    ticketStore.deleteTicket(deletingTicket.value._id)
  }
}

function handleDeleteCancel() {
  showDeleteDialog.value = false
  deletingTicket.value = null
}

// Socket listeners for ticket operations
function setupSocketListeners() {
  // Listen for ticket update responses
  socket.on('ticketController:updateTicket', response => {
    if (response.success) {
      toast.success('Ticket updated successfully!')
      showEditDialog.value = false
      editingTicket.value = null
      ticketStore.getTickets() // Refresh list
    } else {
      toast.error(response.message || 'Failed to update ticket')
    }
  })

  // Listen for ticket assignment responses
  socket.on('ticketController:assignTicket', response => {
    if (response.success) {
      toast.success('Ticket assigned successfully!')
      showAssignDialog.value = false
      assigningTicket.value = null
      selectedAgent.value = ''
      ticketStore.getTickets() // Refresh list
    } else {
      toast.error(response.message || 'Failed to assign ticket')
    }
  })

  // Listen for ticket deletion responses
  socket.on('ticketController:deleteTicket', response => {
    if (response.success) {
      toast.success('Ticket deleted successfully!')
      showDeleteDialog.value = false
      deletingTicket.value = null
      ticketStore.getTickets() // Refresh list
    } else {
      toast.error(response.message || 'Failed to delete ticket')
    }
  })

  // Listen for real-time ticket events
  socket.on('ticket:created', data => {
    toast.info(`New ticket created: ${data.ticket.title}`)
    ticketStore.getTickets() // Refresh list
  })

  socket.on('ticket:updated', data => {
    ticketStore.getTickets() // Refresh list
  })

  socket.on('ticket:assigned', data => {
    ticketStore.getTickets() // Refresh list
  })
}

function removeSocketListeners() {
  socket.off('ticketController:updateTicket')
  socket.off('ticketController:assignTicket')
  socket.off('ticketController:deleteTicket')
  socket.off('ticket:created')
  socket.off('ticket:updated')
  socket.off('ticket:assigned')
}

onMounted(() => {
  if (!canViewAllTickets.value) {
    toast.error('You do not have permission to view all tickets')
    
    return
  }
  setupSocketListeners()
  ticketStore.getTickets()
})

onUnmounted(() => {
  removeSocketListeners()
})
</script>

<template>
  <div>
    <VRow>
      <VCol cols="12">
        <div class="d-flex justify-space-between align-center mb-6">
          <h1 class="text-h4">
            All Tickets
          </h1>
          <VBtn
            color="primary"
            prepend-icon="tabler-plus"
            @click="$router.push({ name: 'tickets-create' })"
          >
            Create Ticket
          </VBtn>
        </div>
      </VCol>
    </VRow>

    <VRow v-if="!canViewAllTickets">
      <VCol cols="12">
        <VAlert
          type="warning"
          variant="tonal"
        >
          You do not have permission to view all tickets. You can only view your own tickets.
        </VAlert>
      </VCol>
    </VRow>

    <VRow v-else>
      <VCol cols="12">
        <TicketList
          :tickets="ticketStore.tickets"
          :loading="ticketStore.loading"
          @refresh="handleRefresh"
          @edit="handleEdit"
          @assign="handleAssign"
          @delete="handleDelete"
        />
      </VCol>
    </VRow>

    <!-- Edit Dialog -->
    <VDialog
      v-model="showEditDialog"
      max-width="800px"
      persistent
    >
      <TicketForm
        :ticket="editingTicket"
        :loading="ticketStore.loading"
        show-advanced-fields
        :agents="agents"
        @submit="handleEditSubmit"
        @cancel="handleEditCancel"
      />
    </VDialog>

    <!-- Assign Dialog -->
    <VDialog
      v-model="showAssignDialog"
      max-width="500px"
      persistent
    >
      <VCard>
        <VCardTitle>Assign Ticket</VCardTitle>
        <VCardText>
          <p class="mb-4">
            Assign ticket "{{ assigningTicket?.title }}" to an agent:
          </p>
          
          <VSelect
            v-model="selectedAgent"
            :items="agents"
            item-title="name"
            item-value="_id"
            label="Select Agent"
            variant="outlined"
            clearable
            placeholder="Unassigned"
          />
        </VCardText>
        <VCardActions>
          <VSpacer />
          <VBtn
            variant="outlined"
            @click="handleAssignCancel"
          >
            Cancel
          </VBtn>
          <VBtn
            color="primary"
            :loading="ticketStore.loading"
            @click="handleAssignSubmit"
          >
            Assign
          </VBtn>
        </VCardActions>
      </VCard>
    </VDialog>

    <!-- Delete Dialog -->
    <VDialog
      v-model="showDeleteDialog"
      max-width="500px"
      persistent
    >
      <VCard>
        <VCardTitle>Delete Ticket</VCardTitle>
        <VCardText>
          <p>Are you sure you want to delete the ticket "{{ deletingTicket?.title }}"?</p>
          <p class="text-error">
            This action cannot be undone.
          </p>
        </VCardText>
        <VCardActions>
          <VSpacer />
          <VBtn
            variant="outlined"
            @click="handleDeleteCancel"
          >
            Cancel
          </VBtn>
          <VBtn
            color="error"
            :loading="ticketStore.loading"
            @click="handleDeleteConfirm"
          >
            Delete
          </VBtn>
        </VCardActions>
      </VCard>
    </VDialog>
  </div>
</template>
