<template>
  <VCard
    class="ticket-card"
    :class="getStatusClass(ticket.status)"
    @click="$emit('click', ticket)"
  >
    <VCardText>
      <div class="d-flex justify-space-between align-start mb-3">
        <div class="flex-grow-1">
          <h6 class="text-h6 mb-1">
            {{ ticket.title }}
          </h6>
          <p class="text-body-2 text-medium-emphasis mb-2">
            {{ truncateText(ticket.description, 100) }}
          </p>
        </div>
        
        <VChip
          :color="getPriorityColor(ticket.priority)"
          size="small"
          variant="tonal"
        >
          {{ ticket.priority }}
        </VChip>
      </div>

      <div class="d-flex align-center justify-space-between mb-3">
        <VChip
          :color="getStatusColor(ticket.status)"
          size="small"
          variant="flat"
        >
          {{ formatStatus(ticket.status) }}
        </VChip>

        <div class="d-flex align-center">
          <VIcon
            :icon="ticket.category?.icon || 'tabler-help-circle'"
            :color="ticket.category?.color || 'primary'"
            size="20"
            class="me-1"
          />
          <span class="text-caption">{{ ticket.category?.name }}</span>
        </div>
      </div>

      <div class="d-flex align-center justify-space-between">
        <div class="d-flex align-center">
          <VAvatar
            size="24"
            class="me-2"
          >
            <VImg
              v-if="ticket.user?.avatar"
              :src="ticket.user.avatar"
              :alt="ticket.user.name"
            />
            <span
              v-else
              class="text-caption"
            >
              {{ getInitials(ticket.user?.name) }}
            </span>
          </VAvatar>
          <span class="text-caption">{{ ticket.user?.name }}</span>
        </div>

        <div class="text-caption text-medium-emphasis">
          {{ formatDate(ticket.createdAt) }}
        </div>
      </div>

      <div
        v-if="ticket.assignedAgent"
        class="d-flex align-center mt-2"
      >
        <VIcon
          icon="tabler-user-check"
          size="16"
          class="me-1"
        />
        <span class="text-caption">
          Assigned to: {{ ticket.assignedAgent.name }}
        </span>
      </div>

      <div
        v-if="ticket.messageCount > 0"
        class="d-flex align-center mt-2"
      >
        <VIcon
          icon="tabler-message"
          size="16"
          class="me-1"
        />
        <span class="text-caption">
          {{ ticket.messageCount }} message{{ ticket.messageCount !== 1 ? 's' : '' }}
        </span>
      </div>
    </VCardText>

    <VCardActions v-if="showActions">
      <VBtn
        v-if="canEdit"
        size="small"
        variant="text"
        @click.stop="$emit('edit', ticket)"
      >
        Edit
      </VBtn>
      
      <VBtn
        v-if="canAssign"
        size="small"
        variant="text"
        @click.stop="$emit('assign', ticket)"
      >
        Assign
      </VBtn>
      
      <VBtn
        v-if="canDelete"
        size="small"
        variant="text"
        color="error"
        @click.stop="$emit('delete', ticket)"
      >
        Delete
      </VBtn>
    </VCardActions>
  </VCard>
</template>

<script setup>
import { computed } from 'vue'
import moment from 'moment'

const props = defineProps({
  ticket: {
    type: Object,
    required: true,
  },
  showActions: {
    type: Boolean,
    default: true,
  },
  canEdit: {
    type: Boolean,
    default: true,
  },
  canAssign: {
    type: Boolean,
    default: false,
  },
  canDelete: {
    type: Boolean,
    default: false,
  },
})

const emit = defineEmits(['click', 'edit', 'assign', 'delete'])

const user = computed(() => {
  const userData = sessionStorage.getItem('userData')
  
  return userData ? JSON.parse(userData) : null
})

function getStatusClass(status) {
  const classes = {
    'open': 'border-s-4 border-warning',
    'in-progress': 'border-s-4 border-info',
    'pending': 'border-s-4 border-secondary',
    'resolved': 'border-s-4 border-success',
    'closed': 'border-s-4 border-surface',
  }
  
  return classes[status] || ''
}

function getStatusColor(status) {
  const colors = {
    'open': 'warning',
    'in-progress': 'info',
    'pending': 'secondary',
    'resolved': 'success',
    'closed': 'surface',
  }
  
  return colors[status] || 'primary'
}

function getPriorityColor(priority) {
  const colors = {
    'low': 'success',
    'medium': 'warning',
    'high': 'error',
    'urgent': 'error',
  }
  
  return colors[priority] || 'primary'
}

function formatStatus(status) {
  return status.split('-').map(word => 
    word.charAt(0).toUpperCase() + word.slice(1),
  ).join(' ')
}

function formatDate(date) {
  return moment(date).fromNow()
}

function truncateText(text, length) {
  if (!text) return ''
  
  return text.length > length ? text.substring(0, length) + '...' : text
}

function getInitials(name) {
  if (!name) return '?'
  
  return name.split(' ').map(n => n[0]).join('').toUpperCase()
}
</script>

<style scoped>
.ticket-card {
  cursor: pointer;
  transition: all 0.2s ease;
}

.ticket-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}
</style>
