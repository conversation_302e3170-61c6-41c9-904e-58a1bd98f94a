<template>
  <div>
    <!-- Filters -->
    <VCard class="mb-6">
      <VCardText>
        <VRow>
          <VCol
            cols="12"
            md="3"
          >
            <VTextField
              v-model="searchQuery"
              label="Search tickets..."
              prepend-inner-icon="tabler-search"
              clearable
              variant="outlined"
              density="compact"
            />
          </VCol>
          
          <VCol
            cols="12"
            md="2"
          >
            <VSelect
              v-model="statusFilter"
              :items="statusOptions"
              label="Status"
              variant="outlined"
              density="compact"
              clearable
            />
          </VCol>
          
          <VCol
            cols="12"
            md="2"
          >
            <VSelect
              v-model="priorityFilter"
              :items="priorityOptions"
              label="Priority"
              variant="outlined"
              density="compact"
              clearable
            />
          </VCol>
          
          <VCol
            cols="12"
            md="2"
          >
            <VSelect
              v-model="categoryFilter"
              :items="categoryOptions"
              item-title="name"
              item-value="_id"
              label="Category"
              variant="outlined"
              density="compact"
              clearable
            />
          </VCol>
          
          <VCol
            cols="12"
            md="2"
          >
            <VSelect
              v-model="sortBy"
              :items="sortOptions"
              label="Sort by"
              variant="outlined"
              density="compact"
            />
          </VCol>
          
          <VCol
            cols="12"
            md="1"
          >
            <VBtn
              icon="tabler-refresh"
              variant="outlined"
              @click="refreshTickets"
            />
          </VCol>
        </VRow>
      </VCardText>
    </VCard>

    <!-- Loading State -->
    <div
      v-if="loading"
      class="text-center py-8"
    >
      <VProgressCircular
        indeterminate
        color="primary"
      />
      <p class="mt-4">
        Loading tickets...
      </p>
    </div>

    <!-- Empty State -->
    <VCard
      v-else-if="filteredTickets.length === 0"
      class="text-center py-8"
    >
      <VCardText>
        <VIcon
          icon="tabler-ticket-off"
          size="64"
          class="mb-4 text-disabled"
        />
        <h3 class="text-h5 mb-2">
          No tickets found
        </h3>
        <p class="text-body-1 text-medium-emphasis">
          {{ searchQuery || statusFilter || priorityFilter || categoryFilter 
            ? 'Try adjusting your filters' 
            : 'Create your first ticket to get started' }}
        </p>
        <VBtn
          v-if="!searchQuery && !statusFilter && !priorityFilter && !categoryFilter"
          color="primary"
          class="mt-4"
          @click="$router.push({ name: 'tickets-create' })"
        >
          Create Ticket
        </VBtn>
      </VCardText>
    </VCard>

    <!-- Tickets Grid -->
    <VRow v-else>
      <VCol
        v-for="ticket in paginatedTickets"
        :key="ticket._id"
        cols="12"
        md="6"
        lg="4"
      >
        <TicketCard
          :ticket="ticket"
          :can-edit="canEditTicket(ticket)"
          :can-assign="canAssignTicket(ticket)"
          :can-delete="canDeleteTicket(ticket)"
          @click="viewTicket"
          @edit="editTicket"
          @assign="assignTicket"
          @delete="deleteTicket"
        />
      </VCol>
    </VRow>

    <!-- Pagination -->
    <div
      v-if="totalPages > 1"
      class="d-flex justify-center mt-6"
    >
      <VPagination
        v-model="currentPage"
        :length="totalPages"
        :total-visible="7"
      />
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useCategoryStore } from '@stores/categories'
import TicketCard from './TicketCard.vue'

const props = defineProps({
  tickets: {
    type: Array,
    default: () => [],
  },
  loading: {
    type: Boolean,
    default: false,
  },
})

const emit = defineEmits(['refresh', 'edit', 'assign', 'delete'])

const router = useRouter()
const categoryStore = useCategoryStore()

// Filters
const searchQuery = ref('')
const statusFilter = ref('')
const priorityFilter = ref('')
const categoryFilter = ref('')
const sortBy = ref('lastActivity')

// Pagination
const currentPage = ref(1)
const itemsPerPage = ref(12)

// Options
const statusOptions = [
  { title: 'Open', value: 'open' },
  { title: 'In Progress', value: 'in-progress' },
  { title: 'Pending', value: 'pending' },
  { title: 'Resolved', value: 'resolved' },
  { title: 'Closed', value: 'closed' },
]

const priorityOptions = [
  { title: 'Low', value: 'low' },
  { title: 'Medium', value: 'medium' },
  { title: 'High', value: 'high' },
  { title: 'Urgent', value: 'urgent' },
]

const sortOptions = [
  { title: 'Last Activity', value: 'lastActivity' },
  { title: 'Created Date', value: 'createdAt' },
  { title: 'Priority', value: 'priority' },
  { title: 'Status', value: 'status' },
  { title: 'Title', value: 'title' },
]

const categoryOptions = computed(() => categoryStore.activeCategories)

const user = computed(() => {
  const userData = sessionStorage.getItem('userData')
  
  return userData ? JSON.parse(userData) : null
})

// Filtered and sorted tickets
const filteredTickets = computed(() => {
  let filtered = Array.isArray(props.tickets) ? [...props.tickets] : []

  // Search filter
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()

    filtered = filtered.filter(ticket => 
      ticket.title.toLowerCase().includes(query) ||
      ticket.description.toLowerCase().includes(query) ||
      ticket.user?.name.toLowerCase().includes(query),
    )
  }

  // Status filter
  if (statusFilter.value) {
    filtered = filtered.filter(ticket => ticket.status === statusFilter.value)
  }

  // Priority filter
  if (priorityFilter.value) {
    filtered = filtered.filter(ticket => ticket.priority === priorityFilter.value)
  }

  // Category filter
  if (categoryFilter.value) {
    filtered = filtered.filter(ticket => ticket.category?._id === categoryFilter.value)
  }

  // Sort
  filtered.sort((a, b) => {
    switch (sortBy.value) {
    case 'lastActivity':
      return new Date(b.lastActivity) - new Date(a.lastActivity)
    case 'createdAt':
      return new Date(b.createdAt) - new Date(a.createdAt)
    case 'priority':
      const priorityOrder = { urgent: 4, high: 3, medium: 2, low: 1 }
      
      return priorityOrder[b.priority] - priorityOrder[a.priority]
    case 'status':
      return a.status.localeCompare(b.status)
    case 'title':
      return a.title.localeCompare(b.title)
    default:
      return 0
    }
  })

  return filtered
})

// Pagination
const totalPages = computed(() => Math.ceil(filteredTickets.value.length / itemsPerPage.value))

const paginatedTickets = computed(() => {
  const start = (currentPage.value - 1) * itemsPerPage.value
  const end = start + itemsPerPage.value
  
  return filteredTickets.value.slice(start, end)
})

// Reset pagination when filters change
watch([searchQuery, statusFilter, priorityFilter, categoryFilter], () => {
  currentPage.value = 1
})

// Permissions
function canEditTicket(ticket) {
  if (!user.value) return false
  
  return user.value.role === 'admin' || 
         user.value.role === 'agent' || 
         ticket.user._id === user.value._id
}

function canAssignTicket(ticket) {
  if (!user.value) return false
  
  return user.value.role === 'admin' || user.value.role === 'agent'
}

function canDeleteTicket(ticket) {
  if (!user.value) return false
  
  return user.value.role === 'admin'
}

// Actions
function viewTicket(ticket) {
  router.push({ name: 'tickets-id', params: { id: ticket._id } })
}

function editTicket(ticket) {
  emit('edit', ticket)
}

function assignTicket(ticket) {
  emit('assign', ticket)
}

function deleteTicket(ticket) {
  emit('delete', ticket)
}

function refreshTickets() {
  emit('refresh')
}

onMounted(() => {
  if (categoryStore.categories.length === 0) {
    categoryStore.getCategories()
  }
})
</script>
