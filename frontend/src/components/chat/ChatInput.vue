<template>
  <VCard class="chat-input-card">
    <VCardText class="pb-2">
      <!-- Typing indicator -->
      <div
        v-if="typingUsers.length > 0"
        class="typing-indicator mb-2"
      >
        <VIcon
          icon="tabler-dots"
          size="16"
          class="me-1 typing-animation"
        />
        <span class="text-caption text-medium-emphasis">
          {{ getTypingText() }}
        </span>
      </div>

      <!-- Message input form -->
      <VForm @submit.prevent="sendMessage">
        <div class="d-flex align-end gap-2">
          <!-- Message type toggle (for agents/admins) -->
          <VTooltip
            v-if="canSendInternalNotes"
            text="Toggle internal note"
          >
            <template #activator="{ props: tooltipProps }">
              <VBtn
                v-bind="tooltipProps"
                :icon="isInternal ? 'tabler-eye-off' : 'tabler-eye'"
                :color="isInternal ? 'warning' : 'default'"
                variant="outlined"
                size="small"
                @click="isInternal = !isInternal"
              />
            </template>
          </VTooltip>

          <!-- File attachment button -->
          <VTooltip text="Attach file">
            <template #activator="{ props: tooltipProps }">
              <VBtn
                v-bind="tooltipProps"
                icon="tabler-paperclip"
                variant="outlined"
                size="small"
                @click="$refs.fileInput.click()"
              />
            </template>
          </VTooltip>

          <!-- Hidden file input -->
          <input
            ref="fileInput"
            type="file"
            multiple
            style="display: none"
            @change="handleFileSelect"
          >

          <!-- Message input -->
          <VTextarea
            v-model="messageText"
            :placeholder="isInternal ? 'Type an internal note...' : 'Type a message...'"
            variant="outlined"
            rows="1"
            auto-grow
            max-rows="4"
            class="flex-grow-1"
            @keydown="handleKeydown"
            @input="handleTyping"
          />

          <!-- Send button -->
          <VBtn
            :disabled="!messageText.trim() || sending"
            :loading="sending"
            icon="tabler-send"
            color="primary"
            @click="sendMessage"
          />
        </div>
      </VForm>

      <!-- File attachments preview -->
      <div
        v-if="selectedFiles.length > 0"
        class="mt-3"
      >
        <VAlert
          type="info"
          variant="tonal"
          density="compact"
          class="mb-3"
        >
          <VIcon
            icon="tabler-paperclip"
            size="16"
            class="me-1"
          />
          {{ selectedFiles.length }} file{{ selectedFiles.length !== 1 ? 's' : '' }} ready to upload
        </VAlert>

        <div class="d-flex flex-wrap gap-2">
          <VCard
            v-for="(file, index) in selectedFiles"
            :key="index"
            variant="outlined"
            class="file-preview-card"
          >
            <VCardText class="pa-2">
              <div class="d-flex align-center">
                <VIcon
                  :icon="getFileIcon(file.type)"
                  :color="getFileColor(file.type)"
                  size="20"
                  class="me-2"
                />

                <div class="flex-grow-1">
                  <div class="text-caption font-weight-medium">
                    {{ file.name }}
                  </div>
                  <div class="text-caption text-medium-emphasis">
                    {{ formatFileSize(file.size) }}
                  </div>
                </div>

                <VBtn
                  icon="tabler-x"
                  size="x-small"
                  variant="text"
                  @click="removeFile(index)"
                />
              </div>

              <!-- Image preview -->
              <div
                v-if="isImageFile(file.type)"
                class="mt-2"
              >
                <VImg
                  :src="getFilePreview(file)"
                  :alt="file.name"
                  max-height="100"
                  class="rounded"
                />
              </div>
            </VCardText>
          </VCard>
        </div>
      </div>

      <!-- Internal note indicator -->
      <div
        v-if="isInternal"
        class="mt-2"
      >
        <VAlert
          type="warning"
          variant="tonal"
          density="compact"
        >
          <VIcon
            icon="tabler-eye-off"
            size="16"
            class="me-1"
          />
          This will be sent as an internal note (only visible to agents and admins)
        </VAlert>
      </div>
    </VCardText>
  </VCard>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { toast } from 'vue3-toastify'

const props = defineProps({
  ticketId: {
    type: String,
    required: true,
  },
  typingUsers: {
    type: Array,
    default: () => [],
  },
  sending: {
    type: Boolean,
    default: false,
  },
})

const emit = defineEmits(['sendMessage', 'startTyping', 'stopTyping'])

const messageText = ref('')
const isInternal = ref(false)
const selectedFiles = ref([])
const typingTimeout = ref(null)
const isTyping = ref(false)

const user = computed(() => {
  const userData = sessionStorage.getItem('userData')
  
  return userData ? JSON.parse(userData) : null
})

const canSendInternalNotes = computed(() => {
  return user.value && ['agent', 'admin'].includes(user.value.role)
})

function sendMessage() {
  if (!messageText.value.trim()) return

  const messageData = {
    content: messageText.value.trim(),
    messageType: 'message',
    isInternal: isInternal.value && canSendInternalNotes.value,
  }

  emit('sendMessage', messageData, selectedFiles.value)

  // Reset form
  messageText.value = ''
  isInternal.value = false
  selectedFiles.value = []

  // Stop typing indicator
  if (isTyping.value) {
    emit('stopTyping', props.ticketId)
    isTyping.value = false
  }
}

function handleKeydown(event) {
  // Send on Ctrl+Enter or Cmd+Enter
  if ((event.ctrlKey || event.metaKey) && event.key === 'Enter') {
    event.preventDefault()
    sendMessage()
  }
}

function handleTyping() {
  // Start typing indicator
  if (!isTyping.value) {
    emit('startTyping', props.ticketId)
    isTyping.value = true
  }

  // Reset typing timeout
  if (typingTimeout.value) {
    clearTimeout(typingTimeout.value)
  }

  // Stop typing after 3 seconds of inactivity
  typingTimeout.value = setTimeout(() => {
    if (isTyping.value) {
      emit('stopTyping', props.ticketId)
      isTyping.value = false
    }
  }, 3000)
}

function handleFileSelect(event) {
  const files = Array.from(event.target.files)

  // Validate file size (max 10MB per file)
  const maxSize = 10 * 1024 * 1024 // 10MB

  // Validate file types
  const allowedTypes = [
    'image/jpeg',
    'image/jpg',
    'image/png',
    'image/gif',
    'image/webp',
    'application/pdf',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'application/vnd.ms-excel',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'text/plain',
    'text/csv',
    'application/zip',
    'application/x-zip-compressed',
  ]

  const validFiles = files.filter(file => {
    if (file.size > maxSize) {
      toast.error(`File ${file.name} is too large. Maximum size is 10MB.`)
      
      return false
    }

    if (!allowedTypes.includes(file.type)) {
      toast.error(`File type not supported: ${file.name}`)
      
      return false
    }

    return true
  })

  selectedFiles.value.push(...validFiles)

  // Clear the input
  event.target.value = ''
}

function removeFile(index) {
  selectedFiles.value.splice(index, 1)
}

function getTypingText() {
  if (props.typingUsers.length === 1) {
    return 'Someone is typing...'
  } else if (props.typingUsers.length === 2) {
    return '2 people are typing...'
  } else {
    return `${props.typingUsers.length} people are typing...`
  }
}

function getFileIcon(mimeType) {
  if (mimeType.startsWith('image/')) return 'tabler-photo'
  if (mimeType.includes('pdf')) return 'tabler-file-type-pdf'
  if (mimeType.includes('word') || mimeType.includes('document')) return 'tabler-file-type-doc'
  if (mimeType.includes('excel') || mimeType.includes('spreadsheet')) return 'tabler-file-type-xls'
  if (mimeType.includes('zip')) return 'tabler-file-zip'
  if (mimeType.startsWith('text/')) return 'tabler-file-text'
  
  return 'tabler-file'
}

function getFileColor(mimeType) {
  if (mimeType.startsWith('image/')) return 'success'
  if (mimeType.includes('pdf')) return 'error'
  if (mimeType.includes('word') || mimeType.includes('document')) return 'info'
  if (mimeType.includes('excel') || mimeType.includes('spreadsheet')) return 'success'
  if (mimeType.includes('zip')) return 'warning'
  
  return 'primary'
}

function formatFileSize(bytes) {
  if (bytes === 0) return '0 Bytes'
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

function isImageFile(mimeType) {
  return mimeType.startsWith('image/')
}

function getFilePreview(file) {
  if (isImageFile(file.type)) {
    return URL.createObjectURL(file)
  }
  
  return null
}

// Stop typing when component unmounts
watch(() => props.ticketId, () => {
  if (isTyping.value) {
    emit('stopTyping', props.ticketId)
    isTyping.value = false
  }
})
</script>

<style scoped>
.chat-input-card {
  position: sticky;
  bottom: 0;
  z-index: 10;
}

.typing-indicator {
  height: 20px;
}

.typing-animation {
  animation: pulse 1.5s ease-in-out infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.file-preview-card {
  max-width: 200px;
}

.file-preview-card .v-img {
  border-radius: 4px;
}
</style>
