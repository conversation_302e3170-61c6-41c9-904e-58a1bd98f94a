<template>
  <div
    class="message-item"
    :class="messageClasses"
  >
    <div class="d-flex align-start">
      <!-- Avatar -->
      <VAvatar
        v-if="!isOwnMessage"
        size="32"
        class="me-3"
      >
        <VImg
          v-if="message.sender?.avatar"
          :src="message.sender.avatar"
          :alt="message.sender.name"
        />
        <span
          v-else
          class="text-caption"
        >
          {{ getInitials(message.sender?.name) }}
        </span>
      </VAvatar>

      <!-- Message Content -->
      <div class="message-content flex-grow-1">
        <!-- Header -->
        <div class="d-flex align-center justify-space-between mb-1">
          <div class="d-flex align-center">
            <span class="text-subtitle-2 me-2">
              {{ message.sender?.name }}
            </span>
            
            <VChip
              v-if="message.sender?.role === 'agent'"
              size="x-small"
              color="info"
              variant="tonal"
            >
              Agent
            </VChip>
            
            <VChip
              v-else-if="message.sender?.role === 'admin'"
              size="x-small"
              color="error"
              variant="tonal"
            >
              Admin
            </VChip>

            <VChip
              v-if="message.isInternal"
              size="x-small"
              color="warning"
              variant="tonal"
              class="ms-1"
            >
              Internal
            </VChip>
          </div>

          <div class="d-flex align-center">
            <span class="text-caption text-medium-emphasis me-2">
              {{ formatTime(message.createdAt) }}
            </span>

            <!-- Message Actions -->
            <VMenu v-if="canEdit || canDelete">
              <template #activator="{ props: menuProps }">
                <VBtn
                  v-bind="menuProps"
                  icon="tabler-dots-vertical"
                  size="x-small"
                  variant="text"
                />
              </template>

              <VList>
                <VListItem
                  v-if="canEdit"
                  @click="$emit('edit', message)"
                >
                  <template #prepend>
                    <VIcon icon="tabler-edit" />
                  </template>
                  <VListItemTitle>Edit</VListItemTitle>
                </VListItem>

                <VListItem
                  v-if="canDelete"
                  @click="$emit('delete', message)"
                >
                  <template #prepend>
                    <VIcon icon="tabler-trash" />
                  </template>
                  <VListItemTitle>Delete</VListItemTitle>
                </VListItem>
              </VList>
            </VMenu>
          </div>
        </div>

        <!-- Message Body -->
        <div
          class="message-body"
          :class="messageBodyClasses"
        >
          <div
            v-if="message.messageType === 'system'"
            class="text-center text-caption text-medium-emphasis"
          >
            <VIcon
              icon="tabler-info-circle"
              size="16"
              class="me-1"
            />
            {{ message.content }}
          </div>

          <div v-else>
            <p class="mb-0">
              {{ message.content }}
            </p>
            
            <!-- Edited indicator -->
            <div
              v-if="message.editedAt"
              class="text-caption text-medium-emphasis mt-1"
            >
              <VIcon
                icon="tabler-edit"
                size="12"
                class="me-1"
              />
              Edited {{ formatTime(message.editedAt) }}
            </div>
          </div>
        </div>

        <!-- Attachments -->
        <div
          v-if="message.attachments && message.attachments.length > 0"
          class="mt-2"
        >
          <div class="d-flex flex-wrap gap-2">
            <VCard
              v-for="attachment in message.attachments"
              :key="attachment._id"
              variant="outlined"
              class="attachment-card"
            >
              <VCardText class="pa-3">
                <div class="d-flex align-center">
                  <VIcon
                    :icon="getFileIcon(attachment.mimeType)"
                    :color="getFileColor(attachment.mimeType)"
                    size="24"
                    class="me-2"
                  />

                  <div class="flex-grow-1">
                    <div class="text-body-2 font-weight-medium">
                      {{ attachment.originalName }}
                    </div>
                    <div class="text-caption text-medium-emphasis">
                      {{ formatFileSize(attachment.size) }}
                    </div>
                  </div>

                  <div class="d-flex align-center">
                    <VBtn
                      icon="tabler-download"
                      size="small"
                      variant="text"
                      @click="downloadAttachment(attachment)"
                    />

                    <VBtn
                      v-if="canDeleteAttachment(attachment)"
                      icon="tabler-trash"
                      size="small"
                      variant="text"
                      color="error"
                      @click="deleteAttachment(attachment)"
                    />
                  </div>
                </div>

                <!-- Image preview for image files -->
                <div
                  v-if="isImageFile(attachment.mimeType)"
                  class="mt-2"
                >
                  <VImg
                    :src="attachment.url"
                    :alt="attachment.originalName"
                    max-height="200"
                    class="rounded cursor-pointer"
                    @click="openImagePreview(attachment)"
                  />
                </div>
              </VCardText>
            </VCard>
          </div>
        </div>

        <!-- Read receipts -->
        <div
          v-if="showReadReceipts && message.readBy && message.readBy.length > 0"
          class="mt-1"
        >
          <div class="d-flex align-center">
            <VIcon
              icon="tabler-checks"
              size="12"
              color="success"
              class="me-1"
            />
            <span class="text-caption text-medium-emphasis">
              Read by {{ getReadByText(message.readBy) }}
            </span>
          </div>
        </div>
      </div>

      <!-- Own message avatar -->
      <VAvatar
        v-if="isOwnMessage"
        size="32"
        class="ms-3"
      >
        <VImg
          v-if="message.sender?.avatar"
          :src="message.sender.avatar"
          :alt="message.sender.name"
        />
        <span
          v-else
          class="text-caption"
        >
          {{ getInitials(message.sender?.name) }}
        </span>
      </VAvatar>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { useMessageStore } from '@stores/messages'
import moment from 'moment'

const props = defineProps({
  message: {
    type: Object,
    required: true,
  },
  showReadReceipts: {
    type: Boolean,
    default: true,
  },
})

const emit = defineEmits(['edit', 'delete'])

const messageStore = useMessageStore()

const user = computed(() => {
  const userData = sessionStorage.getItem('userData')
  
  return userData ? JSON.parse(userData) : null
})

const isOwnMessage = computed(() => {
  return user.value && message.sender?._id === user.value._id
})

const canEdit = computed(() => {
  if (!user.value) return false
  
  // Only sender can edit their own messages within 15 minutes
  if (props.message.sender?._id !== user.value._id) return false
  
  const fifteenMinutesAgo = moment().subtract(15, 'minutes')
  
  return moment(props.message.createdAt).isAfter(fifteenMinutesAgo)
})

const canDelete = computed(() => {
  if (!user.value) return false
  
  // Sender or admin can delete
  return props.message.sender?._id === user.value._id || user.value.role === 'admin'
})

const messageClasses = computed(() => ({
  'own-message': isOwnMessage.value,
  'system-message': props.message.messageType === 'system',
  'internal-message': props.message.isInternal,
}))

const messageBodyClasses = computed(() => ({
  'message-bubble': props.message.messageType !== 'system',
  'own-bubble': isOwnMessage.value && props.message.messageType !== 'system',
  'other-bubble': !isOwnMessage.value && props.message.messageType !== 'system',
  'internal-bubble': props.message.isInternal,
}))

function formatTime(date) {
  return moment(date).format('HH:mm')
}

function getInitials(name) {
  if (!name) return '?'
  
  return name.split(' ').map(n => n[0]).join('').toUpperCase()
}

function getReadByText(readBy) {
  if (!readBy || readBy.length === 0) return ''
  
  const names = readBy.map(read => read.user?.name || 'Unknown').slice(0, 3)
  
  if (readBy.length === 1) {
    return names[0]
  } else if (readBy.length === 2) {
    return `${names[0]} and ${names[1]}`
  } else if (readBy.length === 3) {
    return `${names[0]}, ${names[1]} and ${names[2]}`
  } else {
    return `${names[0]}, ${names[1]} and ${readBy.length - 2} others`
  }
}

function downloadAttachment(attachment) {
  messageStore.downloadAttachment(attachment._id, attachment.originalName)
}

function deleteAttachment(attachment) {
  if (confirm(`Are you sure you want to delete ${attachment.originalName}?`)) {
    messageStore.deleteAttachment(attachment._id)
  }
}

function canDeleteAttachment(attachment) {
  const user = JSON.parse(sessionStorage.getItem('userData'))
  if (!user) return false

  // Only uploader or admin can delete
  return attachment.uploadedBy._id === user._id || user.role === 'admin'
}

function getFileIcon(mimeType) {
  if (mimeType.startsWith('image/')) return 'tabler-photo'
  if (mimeType.includes('pdf')) return 'tabler-file-type-pdf'
  if (mimeType.includes('word') || mimeType.includes('document')) return 'tabler-file-type-doc'
  if (mimeType.includes('excel') || mimeType.includes('spreadsheet')) return 'tabler-file-type-xls'
  if (mimeType.includes('zip')) return 'tabler-file-zip'
  if (mimeType.startsWith('text/')) return 'tabler-file-text'
  
  return 'tabler-file'
}

function getFileColor(mimeType) {
  if (mimeType.startsWith('image/')) return 'success'
  if (mimeType.includes('pdf')) return 'error'
  if (mimeType.includes('word') || mimeType.includes('document')) return 'info'
  if (mimeType.includes('excel') || mimeType.includes('spreadsheet')) return 'success'
  if (mimeType.includes('zip')) return 'warning'
  
  return 'primary'
}

function formatFileSize(bytes) {
  if (bytes === 0) return '0 Bytes'
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

function isImageFile(mimeType) {
  return mimeType.startsWith('image/')
}

function openImagePreview(attachment) {
  // Open image in new tab for preview
  window.open(attachment.url, '_blank')
}
</script>

<style scoped>
.message-item {
  margin-bottom: 16px;
}

.message-item.own-message {
  margin-left: 48px;
}

.message-item:not(.own-message) {
  margin-right: 48px;
}

.message-bubble {
  padding: 12px 16px;
  border-radius: 18px;
  max-width: 100%;
  word-wrap: break-word;
}

.own-bubble {
  background-color: rgb(var(--v-theme-primary));
  color: rgb(var(--v-theme-on-primary));
  border-bottom-right-radius: 4px;
}

.other-bubble {
  background-color: rgb(var(--v-theme-surface-variant));
  color: rgb(var(--v-theme-on-surface-variant));
  border-bottom-left-radius: 4px;
}

.internal-bubble {
  background-color: rgb(var(--v-theme-warning-container));
  color: rgb(var(--v-theme-on-warning-container));
  border: 1px solid rgb(var(--v-theme-warning));
}

.system-message {
  margin: 8px 0;
}

.system-message .message-body {
  background-color: rgb(var(--v-theme-surface-variant));
  padding: 8px 16px;
  border-radius: 12px;
  text-align: center;
}

.attachment-card {
  max-width: 300px;
}

.attachment-card .v-img {
  border-radius: 8px;
}

.attachment-card:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}
</style>
