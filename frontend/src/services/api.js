import axios from 'axios'

const API_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:3000/api/v1'

// Create axios instance
const api = axios.create({
  baseURL: API_URL,
  headers: {
    'Content-Type': 'application/json',
  },
  withCredentials: true,
})

// Add request interceptor to add auth token
api.interceptors.request.use(
  config => {
    const token = sessionStorage.getItem('accessToken')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    
    return config
  },
  error => Promise.reject(error),
)

// Auth API
export const authAPI = {
  register: userData => api.post('/auth/register', userData),
  login: credentials => api.post('/auth/login', credentials),
  logout: () => api.post('/auth/logout'),
  getMe: () => api.get('/auth/me'),
  updateDetails: userData => api.put('/auth/updatedetails', userData),
  updatePassword: passwordData => api.put('/auth/updatepassword', passwordData),
  forgotPassword: email => api.post('/auth/forgotpassword', { email }),
  resetPassword: (resettoken, password) => api.put(`/auth/resetpassword/${resettoken}`, { password }),
}

// Users API
export const usersAPI = {
  getUsers: () => api.get('/users'),
  getUser: id => api.get(`/users/${id}`),
  createUser: userData => api.post('/users', userData),
  updateUser: (id, userData) => api.put(`/users/${id}`, userData),
  deleteUser: id => api.delete(`/users/${id}`),
}

// Tickets API
export const ticketsAPI = {
  getTickets: () => api.get('/tickets'),
  getTicket: id => api.get(`/tickets/${id}`),
  createTicket: ticketData => api.post('/tickets', ticketData),
  updateTicket: (id, ticketData) => api.put(`/tickets/${id}`, ticketData),
  assignTicket: (id, agentId) => api.put(`/tickets/${id}/assign`, { agentId }),
  deleteTicket: id => api.delete(`/tickets/${id}`),
}

// Messages API
export const messagesAPI = {
  getMessages: ticketId => api.get(`/tickets/${ticketId}/messages`),
  createMessage: (ticketId, messageData) => api.post(`/tickets/${ticketId}/messages`, messageData),
  updateMessage: (id, messageData) => api.put(`/messages/${id}`, messageData),
  deleteMessage: id => api.delete(`/messages/${id}`),
  markAsRead: id => api.put(`/messages/${id}/read`),
  getUnreadCount: () => api.get('/messages/unread-count'),
}

// Categories API
export const categoriesAPI = {
  getCategories: () => api.get('/categories'),
  getCategory: id => api.get(`/categories/${id}`),
  createCategory: categoryData => api.post('/categories', categoryData),
  updateCategory: (id, categoryData) => api.put(`/categories/${id}`, categoryData),
  deleteCategory: id => api.delete(`/categories/${id}`),
  getCategoryStats: id => api.get(`/categories/${id}/stats`),
}

export default api
