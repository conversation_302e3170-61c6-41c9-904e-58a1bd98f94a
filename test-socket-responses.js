#!/usr/bin/env node

/**
 * Test Script to Verify Socket Response Format
 * 
 * This script tests that all backend controllers are sending responses
 * in the correct format: { status: 'success'/'error', data: responseData }
 */

const io = require('socket.io-client');

const socket = io('http://localhost:3000', {
  auth: {
    token: 'test-token', // You'll need a real token for actual testing
    userId: 'test-user-id'
  }
});

// Test response format for different controllers
const testControllers = [
  {
    name: 'ticketController',
    events: ['getTickets', 'createTicket', 'updateTicket', 'deleteTicket', 'assignTicket']
  },
  {
    name: 'messageController', 
    events: ['getMessages', 'createMessage', 'updateMessage', 'deleteMessage', 'markAsRead']
  },
  {
    name: 'authController',
    events: ['login', 'register', 'logout', 'getMe', 'forgotPassword', 'resetPassword', 'updateDetails', 'updatePassword']
  },
  {
    name: 'userController',
    events: ['getUsers', 'getUser', 'createUser', 'updateUser', 'deleteUser']
  }
];

function testResponseFormat(controllerName, eventName, response) {
  console.log(`\n🧪 Testing ${controllerName}:${eventName}`);
  
  // Check if response has correct structure
  if (!response || typeof response !== 'object') {
    console.log(`❌ Invalid response type: ${typeof response}`);
    return false;
  }
  
  if (!response.hasOwnProperty('status')) {
    console.log(`❌ Missing 'status' property`);
    return false;
  }
  
  if (response.status !== 'success' && response.status !== 'error') {
    console.log(`❌ Invalid status value: ${response.status}`);
    return false;
  }
  
  if (!response.hasOwnProperty('data')) {
    console.log(`❌ Missing 'data' property`);
    return false;
  }
  
  console.log(`✅ Response format is correct`);
  console.log(`   Status: ${response.status}`);
  console.log(`   Data type: ${typeof response.data}`);
  
  return true;
}

socket.on('connect', () => {
  console.log('🔌 Connected to socket server');
  console.log('📋 Testing socket response formats...\n');
  
  // Note: This is a basic structure test
  // For actual testing, you would need valid authentication and test data
  console.log('ℹ️  This script shows the expected response format structure.');
  console.log('ℹ️  For actual testing, implement with valid auth tokens and test data.\n');
  
  // Example of expected response format
  const exampleSuccessResponse = {
    status: 'success',
    data: {
      message: 'Operation completed successfully',
      // ... actual response data
    }
  };
  
  const exampleErrorResponse = {
    status: 'error', 
    data: {
      message: 'Operation failed',
      error: 'Detailed error information'
    }
  };
  
  console.log('✅ Expected SUCCESS response format:');
  console.log(JSON.stringify(exampleSuccessResponse, null, 2));
  
  console.log('\n❌ Expected ERROR response format:');
  console.log(JSON.stringify(exampleErrorResponse, null, 2));
  
  console.log('\n📊 Controllers and Events Updated:');
  testControllers.forEach(controller => {
    console.log(`\n🎯 ${controller.name}:`);
    controller.events.forEach(event => {
      console.log(`   ✅ ${event}`);
    });
  });
  
  socket.disconnect();
});

socket.on('connect_error', (error) => {
  console.log('❌ Connection failed:', error.message);
});
